/*
 * Copyright © 2018-2021, VideoLAN and dav1d authors
 * Copyright © 2018, Two Orioles, LLC
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TU<PERSON> GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "config.h"

#include <string.h>

#include "common/frame.h"

#include "src/internal.h"
#include "src/tables.h"

#define CDF1(x) (32768-(x))

#define CDF2(a,b) \
    CDF1(a), CDF1(b)
#define CDF3(a,b,c) \
    CDF1(a), CDF2(b,c)
#define CDF4(a,b,c,d) \
    CDF1(a), CDF3(b,c,d)
#define CDF5(a,b,c,d,e) \
    CDF1(a), CDF4(b,c,d,e)
#define CDF6(a,b,c,d,e,f) \
    CDF1(a), CDF5(b,c,d,e,f)
#define CDF7(a,b,c,d,e,f,g) \
    CDF1(a), CDF6(b,c,d,e,f,g)
#define CDF8(a,b,c,d,e,f,g,h) \
    CDF1(a), CDF7(b,c,d,e,f,g,h)
#define CDF9(a,b,c,d,e,f,g,h,i) \
    CDF1(a), CDF8(b,c,d,e,f,g,h,i)
#define CDF10(a,b,c,d,e,f,g,h,i,j) \
    CDF1(a), CDF9(b,c,d,e,f,g,h,i,j)
#define CDF11(a,b,c,d,e,f,g,h,i,j,k) \
    CDF1(a), CDF10(b,c,d,e,f,g,h,i,j,k)
#define CDF12(a,b,c,d,e,f,g,h,i,j,k,l) \
    CDF1(a), CDF11(b,c,d,e,f,g,h,i,j,k,l)
#define CDF13(a,b,c,d,e,f,g,h,i,j,k,l,m) \
    CDF1(a), CDF12(b,c,d,e,f,g,h,i,j,k,l,m)
#define CDF14(a,b,c,d,e,f,g,h,i,j,k,l,m,n) \
    CDF1(a), CDF13(b,c,d,e,f,g,h,i,j,k,l,m,n)
#define CDF15(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o) \
    CDF1(a), CDF14(b,c,d,e,f,g,h,i,j,k,l,m,n,o)

static const CdfModeContext av1_default_cdf = {
    .y_mode = {
        { CDF12(22801, 23489, 24293, 24756, 25601, 26123,
                26606, 27418, 27945, 29228, 29685, 30349) },
        { CDF12(18673, 19845, 22631, 23318, 23950, 24649,
                25527, 27364, 28152, 29701, 29984, 30852) },
        { CDF12(19770, 20979, 23396, 23939, 24241, 24654,
                25136, 27073, 27830, 29360, 29730, 30659) },
        { CDF12(20155, 21301, 22838, 23178, 23261, 23533,
                23703, 24804, 25352, 26575, 27016, 28049) },
    }, .use_filter_intra = {
        [BS_4x4]     = { CDF1( 4621) },
        [BS_4x8]     = { CDF1( 6743) },
        [BS_8x4]     = { CDF1( 5893) },
        [BS_8x8]     = { CDF1( 7866) },
        [BS_8x16]    = { CDF1(12551) },
        [BS_16x8]    = { CDF1( 9394) },
        [BS_16x16]   = { CDF1(12408) },
        [BS_16x32]   = { CDF1(14301) },
        [BS_32x16]   = { CDF1(12756) },
        [BS_32x32]   = { CDF1(22343) },
        [BS_32x64]   = { CDF1(16384) },
        [BS_64x32]   = { CDF1(16384) },
        [BS_64x64]   = { CDF1(16384) },
        [BS_64x128]  = { CDF1(16384) },
        [BS_128x64]  = { CDF1(16384) },
        [BS_128x128] = { CDF1(16384) },
        [BS_4x16]    = { CDF1(12770) },
        [BS_16x4]    = { CDF1(10368) },
        [BS_8x32]    = { CDF1(20229) },
        [BS_32x8]    = { CDF1(18101) },
        [BS_16x64]   = { CDF1(16384) },
        [BS_64x16]   = { CDF1(16384) },
    }, .filter_intra = {
        CDF4(8949, 12776, 17211, 29558),
    }, .uv_mode = {
        {
            { CDF12(22631, 24152, 25378, 25661, 25986, 26520,
                    27055, 27923, 28244, 30059, 30941, 31961) },
            { CDF12( 9513, 26881, 26973, 27046, 27118, 27664,
                    27739, 27824, 28359, 29505, 29800, 31796) },
            { CDF12( 9845,  9915, 28663, 28704, 28757, 28780,
                    29198, 29822, 29854, 30764, 31777, 32029) },
            { CDF12(13639, 13897, 14171, 25331, 25606, 25727,
                    25953, 27148, 28577, 30612, 31355, 32493) },
            { CDF12( 9764,  9835,  9930,  9954, 25386, 27053,
                    27958, 28148, 28243, 31101, 31744, 32363) },
            { CDF12(11825, 13589, 13677, 13720, 15048, 29213,
                    29301, 29458, 29711, 31161, 31441, 32550) },
            { CDF12(14175, 14399, 16608, 16821, 17718, 17775,
                    28551, 30200, 30245, 31837, 32342, 32667) },
            { CDF12(12885, 13038, 14978, 15590, 15673, 15748,
                    16176, 29128, 29267, 30643, 31961, 32461) },
            { CDF12(12026, 13661, 13874, 15305, 15490, 15726,
                    15995, 16273, 28443, 30388, 30767, 32416) },
            { CDF12(19052, 19840, 20579, 20916, 21150, 21467,
                    21885, 22719, 23174, 28861, 30379, 32175) },
            { CDF12(18627, 19649, 20974, 21219, 21492, 21816,
                    22199, 23119, 23527, 27053, 31397, 32148) },
            { CDF12(17026, 19004, 19997, 20339, 20586, 21103,
                    21349, 21907, 22482, 25896, 26541, 31819) },
            { CDF12(12124, 13759, 14959, 14992, 15007, 15051,
                    15078, 15166, 15255, 15753, 16039, 16606) },
        }, {
            { CDF13(10407, 11208, 12900, 13181, 13823, 14175, 14899,
                    15656, 15986, 20086, 20995, 22455, 24212) },
            { CDF13( 4532, 19780, 20057, 20215, 20428, 21071, 21199,
                    21451, 22099, 24228, 24693, 27032, 29472) },
            { CDF13( 5273,  5379, 20177, 20270, 20385, 20439, 20949,
                    21695, 21774, 23138, 24256, 24703, 26679) },
            { CDF13( 6740,  7167,  7662, 14152, 14536, 14785, 15034,
                    16741, 18371, 21520, 22206, 23389, 24182) },
            { CDF13( 4987,  5368,  5928,  6068, 19114, 20315, 21857,
                    22253, 22411, 24911, 25380, 26027, 26376) },
            { CDF13( 5370,  6889,  7247,  7393,  9498, 21114, 21402,
                    21753, 21981, 24780, 25386, 26517, 27176) },
            { CDF13( 4816,  4961,  7204,  7326,  8765,  8930, 20169,
                    20682, 20803, 23188, 23763, 24455, 24940) },
            { CDF13( 6608,  6740,  8529,  9049,  9257,  9356,  9735,
                    18827, 19059, 22336, 23204, 23964, 24793) },
            { CDF13( 5998,  7419,  7781,  8933,  9255,  9549,  9753,
                    10417, 18898, 22494, 23139, 24764, 25989) },
            { CDF13(10660, 11298, 12550, 12957, 13322, 13624, 14040,
                    15004, 15534, 20714, 21789, 23443, 24861) },
            { CDF13(10522, 11530, 12552, 12963, 13378, 13779, 14245,
                    15235, 15902, 20102, 22696, 23774, 25838) },
            { CDF13(10099, 10691, 12639, 13049, 13386, 13665, 14125,
                    15163, 15636, 19676, 20474, 23519, 25208) },
            { CDF13( 3144,  5087,  7382,  7504,  7593,  7690,  7801,
                     8064,  8232,  9248,  9875, 10521, 29048) },
        },
    }, .angle_delta = {
        { CDF6( 2180,  5032,  7567, 22776, 26989, 30217) },
        { CDF6( 2301,  5608,  8801, 23487, 26974, 30330) },
        { CDF6( 3780, 11018, 13699, 19354, 23083, 31286) },
        { CDF6( 4581, 11226, 15147, 17138, 21834, 28397) },
        { CDF6( 1737, 10927, 14509, 19588, 22745, 28823) },
        { CDF6( 2664, 10176, 12485, 17650, 21600, 30495) },
        { CDF6( 2240, 11096, 15453, 20341, 22561, 28917) },
        { CDF6( 3605, 10428, 12459, 17676, 21244, 30655) },
    }, .filter = {
        {
            { CDF2(31935, 32720) }, { CDF2( 5568, 32719) },
            { CDF2(  422,  2938) }, { CDF2(28244, 32608) },
            { CDF2(31206, 31953) }, { CDF2( 4862, 32121) },
            { CDF2(  770,  1152) }, { CDF2(20889, 25637) },
        }, {
            { CDF2(31910, 32724) }, { CDF2( 4120, 32712) },
            { CDF2(  305,  2247) }, { CDF2(27403, 32636) },
            { CDF2(31022, 32009) }, { CDF2( 2963, 32093) },
            { CDF2(  601,   943) }, { CDF2(14969, 21398) },
        },
    }, .newmv_mode = {
        { CDF1(24035) }, { CDF1(16630) }, { CDF1(15339) },
        { CDF1( 8386) }, { CDF1(12222) }, { CDF1( 4676) },
    }, .globalmv_mode = {
        { CDF1( 2175) }, { CDF1( 1054) },
    }, .refmv_mode = {
        { CDF1(23974) }, { CDF1(24188) }, { CDF1(17848) },
        { CDF1(28622) }, { CDF1(24312) }, { CDF1(19923) },
    }, .drl_bit = {
        { CDF1(13104) }, { CDF1(24560) }, { CDF1(18945) },
    }, .comp_inter_mode = {
        { CDF7( 7760, 13823, 15808, 17641, 19156, 20666, 26891) },
        { CDF7(10730, 19452, 21145, 22749, 24039, 25131, 28724) },
        { CDF7(10664, 20221, 21588, 22906, 24295, 25387, 28436) },
        { CDF7(13298, 16984, 20471, 24182, 25067, 25736, 26422) },
        { CDF7(18904, 23325, 25242, 27432, 27898, 28258, 30758) },
        { CDF7(10725, 17454, 20124, 22820, 24195, 25168, 26046) },
        { CDF7(17125, 24273, 25814, 27492, 28214, 28704, 30592) },
        { CDF7(13046, 23214, 24505, 25942, 27435, 28442, 29330) },
    }, .intra = {
        { CDF1(  806) }, { CDF1(16662) }, { CDF1(20186) },
        { CDF1(26538) },
    }, .comp = {
        { CDF1(26828) }, { CDF1(24035) }, { CDF1(12031) },
        { CDF1(10640) }, { CDF1( 2901) },
    }, .comp_dir = {
        { CDF1( 1198) }, { CDF1( 2070) }, { CDF1( 9166) },
        { CDF1( 7499) }, { CDF1(22475) },
    }, .jnt_comp = {
        { CDF1(18244) }, { CDF1(12865) }, { CDF1( 7053) },
        { CDF1(13259) }, { CDF1( 9334) }, { CDF1( 4644) },
    }, .mask_comp = {
        { CDF1(26607) }, { CDF1(22891) }, { CDF1(18840) },
        { CDF1(24594) }, { CDF1(19934) }, { CDF1(22674) },
    }, .wedge_comp = {
        { CDF1(23431) }, { CDF1(13171) }, { CDF1(11470) },
        { CDF1( 9770) }, { CDF1( 9100) }, { CDF1( 8233) },
        { CDF1( 6172) }, { CDF1(11820) }, { CDF1( 7701) },
    }, .wedge_idx = {
        { CDF15( 2438,  4440,  6599,  8663, 11005, 12874, 15751, 18094,
                20359, 22362, 24127, 25702, 27752, 29450, 31171) },
        { CDF15(  806,  3266,  6005,  6738,  7218,  7367,  7771, 14588,
                16323, 17367, 18452, 19422, 22839, 26127, 29629) },
        { CDF15( 2779,  3738,  4683,  7213,  7775,  8017,  8655, 14357,
                17939, 21332, 24520, 27470, 29456, 30529, 31656) },
        { CDF15( 1684,  3625,  5675,  7108,  9302, 11274, 14429, 17144,
                19163, 20961, 22884, 24471, 26719, 28714, 30877) },
        { CDF15( 1142,  3491,  6277,  7314,  8089,  8355,  9023, 13624,
                15369, 16730, 18114, 19313, 22521, 26012, 29550) },
        { CDF15( 2742,  4195,  5727,  8035,  8980,  9336, 10146, 14124,
                17270, 20533, 23434, 25972, 27944, 29570, 31416) },
        { CDF15( 1727,  3948,  6101,  7796,  9841, 12344, 15766, 18944,
                20638, 22038, 23963, 25311, 26988, 28766, 31012) },
        { CDF15(  154,   987,  1925,  2051,  2088,  2111,  2151, 23033,
                23703, 24284, 24985, 25684, 27259, 28883, 30911) },
        { CDF15( 1135,  1322,  1493,  2635,  2696,  2737,  2770, 21016,
                22935, 25057, 27251, 29173, 30089, 30960, 31933) },
    }, .interintra = {
        { CDF1(16384) }, { CDF1(26887) }, { CDF1(27597) },
        { CDF1(30237) },
    }, .interintra_mode = {
        { CDF3(8192, 16384, 24576) },
        { CDF3(1875, 11082, 27332) },
        { CDF3(2473,  9996, 26388) },
        { CDF3(4238, 11537, 25926) },
    }, .interintra_wedge = {
        { CDF1(20036) }, { CDF1(24957) }, { CDF1(26704) },
        { CDF1(27530) }, { CDF1(29564) }, { CDF1(29444) },
        { CDF1(26872) },
    }, .ref = {
        { { CDF1( 4897) }, { CDF1(16973) }, { CDF1(29744) } },
        { { CDF1( 1555) }, { CDF1(16751) }, { CDF1(30279) } },
        { { CDF1( 4236) }, { CDF1(19647) }, { CDF1(31194) } },
        { { CDF1( 8650) }, { CDF1(24773) }, { CDF1(31895) } },
        { { CDF1(  904) }, { CDF1(11014) }, { CDF1(26875) } },
        { { CDF1( 1444) }, { CDF1(15087) }, { CDF1(30304) } },
    }, .comp_fwd_ref = {
        { { CDF1( 4946) }, { CDF1(19891) }, { CDF1(30731) } },
        { { CDF1( 9468) }, { CDF1(22441) }, { CDF1(31059) } },
        { { CDF1( 1503) }, { CDF1(15160) }, { CDF1(27544) } },
    }, .comp_bwd_ref = {
        { { CDF1( 2235) }, { CDF1(17182) }, { CDF1(30606) } },
        { { CDF1( 1423) }, { CDF1(15175) }, { CDF1(30489) } },
    }, .comp_uni_ref = {
        { { CDF1( 5284) }, { CDF1(23152) }, { CDF1(31774) } },
        { { CDF1( 3865) }, { CDF1(14173) }, { CDF1(25120) } },
        { { CDF1( 3128) }, { CDF1(15270) }, { CDF1(26710) } },
    }, .txsz = {
        {
            { CDF1(19968) }, { CDF1(19968) }, { CDF1(24320) },
        }, {
            { CDF2(12272, 30172) }, { CDF2(12272, 30172) },
            { CDF2(18677, 30848) },
        }, {
            { CDF2(12986, 15180) }, { CDF2(12986, 15180) },
            { CDF2(24302, 25602) },
        }, {
            { CDF2( 5782, 11475) }, { CDF2( 5782, 11475) },
            { CDF2(16803, 22759) },
        },
    }, .txpart = {
        { { CDF1(28581) }, { CDF1(23846) }, { CDF1(20847) } },
        { { CDF1(24315) }, { CDF1(18196) }, { CDF1(12133) } },
        { { CDF1(18791) }, { CDF1(10887) }, { CDF1(11005) } },
        { { CDF1(27179) }, { CDF1(20004) }, { CDF1(11281) } },
        { { CDF1(26549) }, { CDF1(19308) }, { CDF1(14224) } },
        { { CDF1(28015) }, { CDF1(21546) }, { CDF1(14400) } },
        { { CDF1(28165) }, { CDF1(22401) }, { CDF1(16088) } },
    }, .txtp_inter1 = {
        { CDF15( 4458,  5560,  7695,  9709, 13330, 14789, 17537, 20266,
                21504, 22848, 23934, 25474, 27727, 28915, 30631) },
        { CDF15( 1645,  2573,  4778,  5711,  7807,  8622, 10522, 15357,
                17674, 20408, 22517, 25010, 27116, 28856, 30749) },
    }, .txtp_inter2 = {
        CDF11(  770,  2421,  5225, 12907, 15819, 18927,
              21561, 24089, 26595, 28526, 30529)
    }, .txtp_inter3 = {
        { CDF1(16384) }, { CDF1( 4167) }, { CDF1( 1998) }, { CDF1(  748) },
    }, .txtp_intra1 = {
        {
            { CDF6( 1535,  8035,  9461, 12751, 23467, 27825) },
            { CDF6(  564,  3335,  9709, 10870, 18143, 28094) },
            { CDF6(  672,  3247,  3676, 11982, 19415, 23127) },
            { CDF6( 5279, 13885, 15487, 18044, 23527, 30252) },
            { CDF6( 4423,  6074,  7985, 10416, 25693, 29298) },
            { CDF6( 1486,  4241,  9460, 10662, 16456, 27694) },
            { CDF6(  439,  2838,  3522,  6737, 18058, 23754) },
            { CDF6( 1190,  4233,  4855, 11670, 20281, 24377) },
            { CDF6( 1045,  4312,  8647, 10159, 18644, 29335) },
            { CDF6(  202,  3734,  4747,  7298, 17127, 24016) },
            { CDF6(  447,  4312,  6819,  8884, 16010, 23858) },
            { CDF6(  277,  4369,  5255,  8905, 16465, 22271) },
            { CDF6( 3409,  5436, 10599, 15599, 19687, 24040) },
        }, {
            { CDF6( 1870, 13742, 14530, 16498, 23770, 27698) },
            { CDF6(  326,  8796, 14632, 15079, 19272, 27486) },
            { CDF6(  484,  7576,  7712, 14443, 19159, 22591) },
            { CDF6( 1126, 15340, 15895, 17023, 20896, 30279) },
            { CDF6(  655,  4854,  5249,  5913, 22099, 27138) },
            { CDF6( 1299,  6458,  8885,  9290, 14851, 25497) },
            { CDF6(  311,  5295,  5552,  6885, 16107, 22672) },
            { CDF6(  883,  8059,  8270, 11258, 17289, 21549) },
            { CDF6(  741,  7580,  9318, 10345, 16688, 29046) },
            { CDF6(  110,  7406,  7915,  9195, 16041, 23329) },
            { CDF6(  363,  7974,  9357, 10673, 15629, 24474) },
            { CDF6(  153,  7647,  8112,  9936, 15307, 19996) },
            { CDF6( 3511,  6332, 11165, 15335, 19323, 23594) },
        },
    }, .txtp_intra2 = {
        {
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
        }, {
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
            { CDF4( 6554, 13107, 19661, 26214) },
        }, {
            { CDF4( 1127, 12814, 22772, 27483) },
            { CDF4(  145,  6761, 11980, 26667) },
            { CDF4(  362,  5887, 11678, 16725) },
            { CDF4(  385, 15213, 18587, 30693) },
            { CDF4(   25,  2914, 23134, 27903) },
            { CDF4(   60,  4470, 11749, 23991) },
            { CDF4(   37,  3332, 14511, 21448) },
            { CDF4(  157,  6320, 13036, 17439) },
            { CDF4(  119,  6719, 12906, 29396) },
            { CDF4(   47,  5537, 12576, 21499) },
            { CDF4(  269,  6076, 11258, 23115) },
            { CDF4(   83,  5615, 12001, 17228) },
            { CDF4( 1968,  5556, 12023, 18547) },
        },
    }, .skip = {
        { CDF1(31671) }, { CDF1(16515) }, { CDF1( 4576) },
    }, .skip_mode = {
        { CDF1(32621) }, { CDF1(20708) }, { CDF1( 8127) },
    }, .partition = {
        {
            // 128x128 -> 64x64
            { CDF7(27899, 28219, 28529, 32484, 32539, 32619, 32639) },
            { CDF7( 6607,  6990,  8268, 32060, 32219, 32338, 32371) },
            { CDF7( 5429,  6676,  7122, 32027, 32227, 32531, 32582) },
            { CDF7(  711,   966,  1172, 32448, 32538, 32617, 32664) },
        }, {
            // 64x64 -> 32x32
            { CDF9(20137, 21547, 23078, 29566, 29837,
                   30261, 30524, 30892, 31724) },
            { CDF9( 6732,  7490,  9497, 27944, 28250,
                   28515, 28969, 29630, 30104) },
            { CDF9( 5945,  7663,  8348, 28683, 29117,
                   29749, 30064, 30298, 32238) },
            { CDF9(  870,  1212,  1487, 31198, 31394,
                   31574, 31743, 31881, 32332) },
        }, {
            // 32x32 -> 16x16
            { CDF9(18462, 20920, 23124, 27647, 28227,
                   29049, 29519, 30178, 31544) },
            { CDF9( 7689,  9060, 12056, 24992, 25660,
                   26182, 26951, 28041, 29052) },
            { CDF9( 6015,  9009, 10062, 24544, 25409,
                   26545, 27071, 27526, 32047) },
            { CDF9( 1394,  2208,  2796, 28614, 29061,
                   29466, 29840, 30185, 31899) },
        }, {
            // 16x16 -> 8x8
            { CDF9(15597, 20929, 24571, 26706, 27664,
                   28821, 29601, 30571, 31902) },
            { CDF9( 7925, 11043, 16785, 22470, 23971,
                   25043, 26651, 28701, 29834) },
            { CDF9( 5414, 13269, 15111, 20488, 22360,
                   24500, 25537, 26336, 32117) },
            { CDF9( 2662,  6362,  8614, 20860, 23053,
                   24778, 26436, 27829, 31171) },
        }, {
            // 8x8 -> 4x4 only supports the four legacy partition types
            { CDF3(19132, 25510, 30392) },
            { CDF3(13928, 19855, 28540) },
            { CDF3(12522, 23679, 28629) },
            { CDF3( 9896, 18783, 25853) },
        },
    }, .seg_pred = {
        { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
    }, .seg_id = {
        { CDF7( 5622,  7893, 16093, 18233, 27809, 28373, 32533) },
        { CDF7(14274, 18230, 22557, 24935, 29980, 30851, 32344) },
        { CDF7(27527, 28487, 28723, 28890, 32397, 32647, 32679) },
    }, .cfl_sign = {
        CDF7( 1418,  2123, 13340, 18405, 26972, 28343, 32294)
    }, .cfl_alpha = {
        { CDF15( 7637, 20719, 31401, 32481, 32657, 32688, 32692, 32696,
                32700, 32704, 32708, 32712, 32716, 32720, 32724) },
        { CDF15(14365, 23603, 28135, 31168, 32167, 32395, 32487, 32573,
                32620, 32647, 32668, 32672, 32676, 32680, 32684) },
        { CDF15(11532, 22380, 28445, 31360, 32349, 32523, 32584, 32649,
                32673, 32677, 32681, 32685, 32689, 32693, 32697) },
        { CDF15(26990, 31402, 32282, 32571, 32692, 32696, 32700, 32704,
                32708, 32712, 32716, 32720, 32724, 32728, 32732) },
        { CDF15(17248, 26058, 28904, 30608, 31305, 31877, 32126, 32321,
                32394, 32464, 32516, 32560, 32576, 32593, 32622) },
        { CDF15(14738, 21678, 25779, 27901, 29024, 30302, 30980, 31843,
                32144, 32413, 32520, 32594, 32622, 32656, 32660) },
    }, .restore_wiener = {
        CDF1(11570)
    }, .restore_sgrproj = {
        CDF1(16855)
    }, .restore_switchable = {
        CDF2( 9413, 22581)
    }, .delta_q = {
        CDF3(28160, 32120, 32677)
    }, .delta_lf = {
        { CDF3(28160, 32120, 32677) },
        { CDF3(28160, 32120, 32677) },
        { CDF3(28160, 32120, 32677) },
        { CDF3(28160, 32120, 32677) },
        { CDF3(28160, 32120, 32677) },
    }, .motion_mode = {
        [BS_8x8]     = { CDF2( 7651, 24760) },
        [BS_8x16]    = { CDF2( 4738, 24765) },
        [BS_8x32]    = { CDF2(28799, 31390) },
        [BS_16x8]    = { CDF2( 5391, 25528) },
        [BS_16x16]   = { CDF2(19419, 26810) },
        [BS_16x32]   = { CDF2( 5123, 23606) },
        [BS_16x64]   = { CDF2(28973, 31594) },
        [BS_32x8]    = { CDF2(26431, 30774) },
        [BS_32x16]   = { CDF2(11606, 24308) },
        [BS_32x32]   = { CDF2(26260, 29116) },
        [BS_32x64]   = { CDF2(20360, 28062) },
        [BS_64x16]   = { CDF2(29742, 31203) },
        [BS_64x32]   = { CDF2(21679, 26830) },
        [BS_64x64]   = { CDF2(29516, 30701) },
        [BS_64x128]  = { CDF2(28898, 30397) },
        [BS_128x64]  = { CDF2(30878, 31335) },
        [BS_128x128] = { CDF2(32507, 32558) },
    }, .obmc = {
        [BS_8x8]     = { CDF1(10437) },
        [BS_8x16]    = { CDF1( 9371) },
        [BS_8x32]    = { CDF1(23664) },
        [BS_16x8]    = { CDF1( 9301) },
        [BS_16x16]   = { CDF1(17432) },
        [BS_16x32]   = { CDF1(14423) },
        [BS_16x64]   = { CDF1(24008) },
        [BS_32x8]    = { CDF1(20901) },
        [BS_32x16]   = { CDF1(15142) },
        [BS_32x32]   = { CDF1(25817) },
        [BS_32x64]   = { CDF1(22823) },
        [BS_64x16]   = { CDF1(26879) },
        [BS_64x32]   = { CDF1(22083) },
        [BS_64x64]   = { CDF1(30128) },
        [BS_64x128]  = { CDF1(31014) },
        [BS_128x64]  = { CDF1(31560) },
        [BS_128x128] = { CDF1(32638) },
    }, .pal_y = {
        { { CDF1(31676) }, { CDF1( 3419) }, { CDF1( 1261) } },
        { { CDF1(31912) }, { CDF1( 2859) }, { CDF1(  980) } },
        { { CDF1(31823) }, { CDF1( 3400) }, { CDF1(  781) } },
        { { CDF1(32030) }, { CDF1( 3561) }, { CDF1(  904) } },
        { { CDF1(32309) }, { CDF1( 7337) }, { CDF1( 1462) } },
        { { CDF1(32265) }, { CDF1( 4015) }, { CDF1( 1521) } },
        { { CDF1(32450) }, { CDF1( 7946) }, { CDF1(  129) } },
    }, .pal_sz = {
        {
            { CDF6( 7952, 13000, 18149, 21478, 25527, 29241) },
            { CDF6( 7139, 11421, 16195, 19544, 23666, 28073) },
            { CDF6( 7788, 12741, 17325, 20500, 24315, 28530) },
            { CDF6( 8271, 14064, 18246, 21564, 25071, 28533) },
            { CDF6(12725, 19180, 21863, 24839, 27535, 30120) },
            { CDF6( 9711, 14888, 16923, 21052, 25661, 27875) },
            { CDF6(14940, 20797, 21678, 24186, 27033, 28999) },
        }, {
            { CDF6( 8713, 19979, 27128, 29609, 31331, 32272) },
            { CDF6( 5839, 15573, 23581, 26947, 29848, 31700) },
            { CDF6( 4426, 11260, 17999, 21483, 25863, 29430) },
            { CDF6( 3228,  9464, 14993, 18089, 22523, 27420) },
            { CDF6( 3768,  8886, 13091, 17852, 22495, 27207) },
            { CDF6( 2464,  8451, 12861, 21632, 25525, 28555) },
            { CDF6( 1269,  5435, 10433, 18963, 21700, 25865) },
        },
    }, .pal_uv = {
        { CDF1(32461) }, { CDF1(21488) },
    }, .color_map = {
        { /* y */
            {
                { CDF1(28710) }, { CDF1(16384) }, { CDF1(10553) },
                { CDF1(27036) }, { CDF1(31603) },
            }, {
                { CDF2(27877, 30490) }, { CDF2(11532, 25697) },
                { CDF2( 6544, 30234) }, { CDF2(23018, 28072) },
                { CDF2(31915, 32385) },
            }, {
                { CDF3(25572, 28046, 30045) },
                { CDF3( 9478, 21590, 27256) },
                { CDF3( 7248, 26837, 29824) },
                { CDF3(19167, 24486, 28349) },
                { CDF3(31400, 31825, 32250) },
            }, {
                { CDF4(24779, 26955, 28576, 30282) },
                { CDF4( 8669, 20364, 24073, 28093) },
                { CDF4( 4255, 27565, 29377, 31067) },
                { CDF4(19864, 23674, 26716, 29530) },
                { CDF4(31646, 31893, 32147, 32426) },
            }, {
                { CDF5(23132, 25407, 26970, 28435, 30073) },
                { CDF5( 7443, 17242, 20717, 24762, 27982) },
                { CDF5( 6300, 24862, 26944, 28784, 30671) },
                { CDF5(18916, 22895, 25267, 27435, 29652) },
                { CDF5(31270, 31550, 31808, 32059, 32353) },
            }, {
                { CDF6(23105, 25199, 26464, 27684, 28931, 30318) },
                { CDF6( 6950, 15447, 18952, 22681, 25567, 28563) },
                { CDF6( 7560, 23474, 25490, 27203, 28921, 30708) },
                { CDF6(18544, 22373, 24457, 26195, 28119, 30045) },
                { CDF6(31198, 31451, 31670, 31882, 32123, 32391) },
            }, {
                { CDF7(21689, 23883, 25163, 26352, 27506, 28827, 30195) },
                { CDF7( 6892, 15385, 17840, 21606, 24287, 26753, 29204) },
                { CDF7( 5651, 23182, 25042, 26518, 27982, 29392, 30900) },
                { CDF7(19349, 22578, 24418, 25994, 27524, 29031, 30448) },
                { CDF7(31028, 31270, 31504, 31705, 31927, 32153, 32392) },
            },
        }, { /* uv */
            {
                { CDF1(29089) }, { CDF1(16384) }, { CDF1( 8713) },
                { CDF1(29257) }, { CDF1(31610) },
            }, {
                { CDF2(25257, 29145) }, { CDF2(12287, 27293) },
                { CDF2( 7033, 27960) }, { CDF2(20145, 25405) },
                { CDF2(30608, 31639) },
            }, {
                { CDF3(24210, 27175, 29903) },
                { CDF3( 9888, 22386, 27214) },
                { CDF3( 5901, 26053, 29293) },
                { CDF3(18318, 22152, 28333) },
                { CDF3(30459, 31136, 31926) },
            }, {
                { CDF4(22980, 25479, 27781, 29986) },
                { CDF4( 8413, 21408, 24859, 28874) },
                { CDF4( 2257, 29449, 30594, 31598) },
                { CDF4(19189, 21202, 25915, 28620) },
                { CDF4(31844, 32044, 32281, 32518) },
            }, {
                { CDF5(22217, 24567, 26637, 28683, 30548) },
                { CDF5( 7307, 16406, 19636, 24632, 28424) },
                { CDF5( 4441, 25064, 26879, 28942, 30919) },
                { CDF5(17210, 20528, 23319, 26750, 29582) },
                { CDF5(30674, 30953, 31396, 31735, 32207) },
            }, {
                { CDF6(21239, 23168, 25044, 26962, 28705, 30506) },
                { CDF6( 6545, 15012, 18004, 21817, 25503, 28701) },
                { CDF6( 3448, 26295, 27437, 28704, 30126, 31442) },
                { CDF6(15889, 18323, 21704, 24698, 26976, 29690) },
                { CDF6(30988, 31204, 31479, 31734, 31983, 32325) },
            }, {
                { CDF7(21442, 23288, 24758, 26246, 27649, 28980, 30563) },
                { CDF7( 5863, 14933, 17552, 20668, 23683, 26411, 29273) },
                { CDF7( 3415, 25810, 26877, 27990, 29223, 30394, 31618) },
                { CDF7(17965, 20084, 22232, 23974, 26274, 28402, 30390) },
                { CDF7(31190, 31329, 31516, 31679, 31825, 32026, 32322) },
            },
        },
    }, .intrabc = {
        CDF1(30531)
    },
};

static const CdfMvComponent default_mv_component_cdf = {
    .classes = {
        CDF10(28672, 30976, 31858, 32320, 32551,
              32656, 32740, 32757, 32762, 32767)
    }, .class0 = {
        CDF1(27648)
    }, .classN = {
        { CDF1(17408) }, { CDF1(17920) }, { CDF1(18944) },
        { CDF1(20480) }, { CDF1(22528) }, { CDF1(24576) },
        { CDF1(28672) }, { CDF1(29952) }, { CDF1(29952) },
        { CDF1(30720) },
    }, .class0_fp = {
        { CDF3(16384, 24576, 26624) },
        { CDF3(12288, 21248, 24128) },
    }, .classN_fp = {
        CDF3( 8192, 17408, 21248)
    }, .class0_hp = {
        CDF1(20480)
    }, .classN_hp = {
        CDF1(16384)
    }, .sign = {
        CDF1(16384)
    },
};

static const uint16_t ALIGN(default_mv_joint_cdf[N_MV_JOINTS], 8) = {
    CDF3( 4096, 11264, 19328)
};

static const uint16_t ALIGN(default_kf_y_mode_cdf[5][5][N_INTRA_PRED_MODES + 3], 32) = {
    {
        { CDF12(15588, 17027, 19338, 20218, 20682, 21110,
                21825, 23244, 24189, 28165, 29093, 30466) },
        { CDF12(12016, 18066, 19516, 20303, 20719, 21444,
                21888, 23032, 24434, 28658, 30172, 31409) },
        { CDF12(10052, 10771, 22296, 22788, 23055, 23239,
                24133, 25620, 26160, 29336, 29929, 31567) },
        { CDF12(14091, 15406, 16442, 18808, 19136, 19546,
                19998, 22096, 24746, 29585, 30958, 32462) },
        { CDF12(12122, 13265, 15603, 16501, 18609, 20033,
                22391, 25583, 26437, 30261, 31073, 32475) },
    }, {
        { CDF12(10023, 19585, 20848, 21440, 21832, 22760,
                23089, 24023, 25381, 29014, 30482, 31436) },
        { CDF12( 5983, 24099, 24560, 24886, 25066, 25795,
                25913, 26423, 27610, 29905, 31276, 31794) },
        { CDF12( 7444, 12781, 20177, 20728, 21077, 21607,
                22170, 23405, 24469, 27915, 29090, 30492) },
        { CDF12( 8537, 14689, 15432, 17087, 17408, 18172,
                18408, 19825, 24649, 29153, 31096, 32210) },
        { CDF12( 7543, 14231, 15496, 16195, 17905, 20717,
                21984, 24516, 26001, 29675, 30981, 31994) },
    }, {
        { CDF12(12613, 13591, 21383, 22004, 22312, 22577,
                23401, 25055, 25729, 29538, 30305, 32077) },
        { CDF12( 9687, 13470, 18506, 19230, 19604, 20147,
                20695, 22062, 23219, 27743, 29211, 30907) },
        { CDF12( 6183,  6505, 26024, 26252, 26366, 26434,
                27082, 28354, 28555, 30467, 30794, 32086) },
        { CDF12(10718, 11734, 14954, 17224, 17565, 17924,
                18561, 21523, 23878, 28975, 30287, 32252) },
        { CDF12( 9194,  9858, 16501, 17263, 18424, 19171,
                21563, 25961, 26561, 30072, 30737, 32463) },
    }, {
        { CDF12(12602, 14399, 15488, 18381, 18778, 19315,
                19724, 21419, 25060, 29696, 30917, 32409) },
        { CDF12( 8203, 13821, 14524, 17105, 17439, 18131,
                18404, 19468, 25225, 29485, 31158, 32342) },
        { CDF12( 8451,  9731, 15004, 17643, 18012, 18425,
                19070, 21538, 24605, 29118, 30078, 32018) },
        { CDF12( 7714,  9048,  9516, 16667, 16817, 16994,
                17153, 18767, 26743, 30389, 31536, 32528) },
        { CDF12( 8843, 10280, 11496, 15317, 16652, 17943,
                19108, 22718, 25769, 29953, 30983, 32485) },
    }, {
        { CDF12(12578, 13671, 15979, 16834, 19075, 20913,
                22989, 25449, 26219, 30214, 31150, 32477) },
        { CDF12( 9563, 13626, 15080, 15892, 17756, 20863,
                22207, 24236, 25380, 29653, 31143, 32277) },
        { CDF12( 8356,  8901, 17616, 18256, 19350, 20106,
                22598, 25947, 26466, 29900, 30523, 32261) },
        { CDF12(10835, 11815, 13124, 16042, 17018, 18039,
                18947, 22753, 24615, 29489, 30883, 32482) },
        { CDF12( 7618,  8288,  9859, 10509, 15386, 18657,
                22903, 28776, 29180, 31355, 31802, 32593) },
    },
};

static const CdfCoefContext av1_default_coef_cdf[4] = {
    [0] = {
        .skip = {
            {
                { CDF1(31849) }, { CDF1( 5892) }, { CDF1(12112) },
                { CDF1(21935) }, { CDF1(20289) }, { CDF1(27473) },
                { CDF1(32487) }, { CDF1( 7654) }, { CDF1(19473) },
                { CDF1(29984) }, { CDF1( 9961) }, { CDF1(30242) },
                { CDF1(32117) },
            }, {
                { CDF1(31548) }, { CDF1( 1549) }, { CDF1(10130) },
                { CDF1(16656) }, { CDF1(18591) }, { CDF1(26308) },
                { CDF1(32537) }, { CDF1( 5403) }, { CDF1(18096) },
                { CDF1(30003) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(29957) }, { CDF1( 5391) }, { CDF1(18039) },
                { CDF1(23566) }, { CDF1(22431) }, { CDF1(25822) },
                { CDF1(32197) }, { CDF1( 3778) }, { CDF1(15336) },
                { CDF1(28981) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(17920) }, { CDF1( 1818) }, { CDF1( 7282) },
                { CDF1(25273) }, { CDF1(10923) }, { CDF1(31554) },
                { CDF1(32624) }, { CDF1( 1366) }, { CDF1(15628) },
                { CDF1(30462) }, { CDF1(  146) }, { CDF1( 5132) },
                { CDF1(31657) },
            }, {
                { CDF1( 6308) }, { CDF1(  117) }, { CDF1( 1638) },
                { CDF1( 2161) }, { CDF1(16384) }, { CDF1(10923) },
                { CDF1(30247) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            },
        }, .eob_bin_16 = {
            {
                { CDF4(  840,  1039,  1980,  4895) },
                { CDF4(  370,   671,  1883,  4471) },
            }, {
                { CDF4( 3247,  4950,  9688, 14563) },
                { CDF4( 1904,  3354,  7763, 14647) },
            },
        }, .eob_bin_32 = {
            {
                { CDF5(  400,   520,   977,  2102,  6542) },
                { CDF5(  210,   405,  1315,  3326,  7537) },
            }, {
                { CDF5( 2636,  4273,  7588, 11794, 20401) },
                { CDF5( 1786,  3179,  6902, 11357, 19054) },
            },
        }, .eob_bin_64 = {
            {
                { CDF6(  329,   498,  1101,  1784,  3265,  7758) },
                { CDF6(  335,   730,  1459,  5494,  8755, 12997) },
            }, {
                { CDF6( 3505,  5304, 10086, 13814, 17684, 23370) },
                { CDF6( 1563,  2700,  4876, 10911, 14706, 22480) },
            },
        }, .eob_bin_128 = {
            {
                { CDF7(  219,   482,  1140,  2091,  3680,  6028, 12586) },
                { CDF7(  371,   699,  1254,  4830,  9479, 12562, 17497) },
            }, {
                { CDF7( 5245,  7456, 12880, 15852, 20033, 23932, 27608) },
                { CDF7( 2054,  3472,  5869, 14232, 18242, 20590, 26752) },
            },
        }, .eob_bin_256 = {
            {
                { CDF8(  310,   584,  1887,  3589,
                        6168,  8611, 11352, 15652) },
                { CDF8(  998,  1850,  2998,  5604,
                       17341, 19888, 22899, 25583) },
            }, {
                { CDF8( 2520,  3240,  5952,  8870,
                       12577, 17558, 19954, 24168) },
                { CDF8( 2203,  4130,  7435, 10739,
                       20652, 23681, 25609, 27261) },
            },
        }, .eob_bin_512 = {
            { CDF9(  641,   983,  3707,  5430, 10234,
                   14958, 18788, 23412, 26061) },
            { CDF9( 5095,  6446,  9996, 13354, 16017,
                   17986, 20919, 26129, 29140) },
        }, .eob_bin_1024 = {
            { CDF10(  393,   421,   751,  1623,  3160,
                     6352, 13345, 18047, 22571, 25830) },
            { CDF10( 1865,  1988,  2930,  4242, 10533,
                    16538, 21354, 27255, 28546, 31784) },
        }, .eob_hi_bit = {
            {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16961) },
                    { CDF1(17223) }, { CDF1( 7621) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(19069) },
                    { CDF1(22525) }, { CDF1(13377) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20401) },
                    { CDF1(17025) }, { CDF1(12845) }, { CDF1(12873) },
                    { CDF1(14094) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20681) },
                    { CDF1(20701) }, { CDF1(15250) }, { CDF1(15017) },
                    { CDF1(14928) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(23905) },
                    { CDF1(17194) }, { CDF1(16170) }, { CDF1(17695) },
                    { CDF1(13826) }, { CDF1(15810) }, { CDF1(12036) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(23959) },
                    { CDF1(20799) }, { CDF1(19021) }, { CDF1(16203) },
                    { CDF1(17886) }, { CDF1(14144) }, { CDF1(12010) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(27399) },
                    { CDF1(16327) }, { CDF1(18071) }, { CDF1(19584) },
                    { CDF1(20721) }, { CDF1(18432) }, { CDF1(19560) },
                    { CDF1(10150) }, { CDF1( 8805) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(24932) },
                    { CDF1(20833) }, { CDF1(12027) }, { CDF1(16670) },
                    { CDF1(19914) }, { CDF1(15106) }, { CDF1(17662) },
                    { CDF1(13783) }, { CDF1(28756) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(23406) },
                    { CDF1(21845) }, { CDF1(18432) }, { CDF1(16384) },
                    { CDF1(17096) }, { CDF1(12561) }, { CDF1(17320) },
                    { CDF1(22395) }, { CDF1(21370) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            },
        }, .eob_base_tok = {
            {
                {
                    { CDF2(17837, 29055) }, { CDF2(29600, 31446) },
                    { CDF2(30844, 31878) }, { CDF2(24926, 28948) },
                }, {
                    { CDF2(21365, 30026) }, { CDF2(30512, 32423) },
                    { CDF2(31658, 32621) }, { CDF2(29630, 31881) },
                },
            }, {
                {
                    { CDF2( 5717, 26477) }, { CDF2(30491, 31703) },
                    { CDF2(31550, 32158) }, { CDF2(29648, 31491) },
                }, {
                    { CDF2(12608, 27820) }, { CDF2(30680, 32225) },
                    { CDF2(30809, 32335) }, { CDF2(31299, 32423) },
                },
            }, {
                {
                    { CDF2( 1786, 12612) }, { CDF2(30663, 31625) },
                    { CDF2(32339, 32468) }, { CDF2(31148, 31833) },
                }, {
                    { CDF2(18857, 23865) }, { CDF2(31428, 32428) },
                    { CDF2(31744, 32373) }, { CDF2(31775, 32526) },
                },
            }, {
                {
                    { CDF2( 1787,  2532) }, { CDF2(30832, 31662) },
                    { CDF2(31824, 32682) }, { CDF2(32133, 32569) },
                }, {
                    { CDF2(13751, 22235) }, { CDF2(32089, 32409) },
                    { CDF2(27084, 27920) }, { CDF2(29291, 32594) },
                },
            }, {
                {
                    { CDF2( 1725,  3449) }, { CDF2(31102, 31935) },
                    { CDF2(32457, 32613) }, { CDF2(32412, 32649) },
                }, {
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                },
            },
        }, .base_tok = {
            {
                {
                    { CDF3( 4034,  8930, 12727) },
                    { CDF3(18082, 29741, 31877) },
                    { CDF3(12596, 26124, 30493) },
                    { CDF3( 9446, 21118, 27005) },
                    { CDF3( 6308, 15141, 21279) },
                    { CDF3( 2463,  6357,  9783) },
                    { CDF3(20667, 30546, 31929) },
                    { CDF3(13043, 26123, 30134) },
                    { CDF3( 8151, 18757, 24778) },
                    { CDF3( 5255, 12839, 18632) },
                    { CDF3( 2820,  7206, 11161) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(15736, 27553, 30604) },
                    { CDF3(11210, 23794, 28787) },
                    { CDF3( 5947, 13874, 19701) },
                    { CDF3( 4215,  9323, 13891) },
                    { CDF3( 2833,  6462, 10059) },
                    { CDF3(19605, 30393, 31582) },
                    { CDF3(13523, 26252, 30248) },
                    { CDF3( 8446, 18622, 24512) },
                    { CDF3( 3818, 10343, 15974) },
                    { CDF3( 1481,  4117,  6796) },
                    { CDF3(22649, 31302, 32190) },
                    { CDF3(14829, 27127, 30449) },
                    { CDF3( 8313, 17702, 23304) },
                    { CDF3( 3022,  8301, 12786) },
                    { CDF3( 1536,  4412,  7184) },
                    { CDF3(22354, 29774, 31372) },
                    { CDF3(14723, 25472, 29214) },
                    { CDF3( 6673, 13745, 18662) },
                    { CDF3( 2068,  5766,  9322) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 6302, 16444, 21761) },
                    { CDF3(23040, 31538, 32475) },
                    { CDF3(15196, 28452, 31496) },
                    { CDF3(10020, 22946, 28514) },
                    { CDF3( 6533, 16862, 23501) },
                    { CDF3( 3538,  9816, 15076) },
                    { CDF3(24444, 31875, 32525) },
                    { CDF3(15881, 28924, 31635) },
                    { CDF3( 9922, 22873, 28466) },
                    { CDF3( 6527, 16966, 23691) },
                    { CDF3( 4114, 11303, 17220) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(20201, 30770, 32209) },
                    { CDF3(14754, 28071, 31258) },
                    { CDF3( 8378, 20186, 26517) },
                    { CDF3( 5916, 15299, 21978) },
                    { CDF3( 4268, 11583, 17901) },
                    { CDF3(24361, 32025, 32581) },
                    { CDF3(18673, 30105, 31943) },
                    { CDF3(10196, 22244, 27576) },
                    { CDF3( 5495, 14349, 20417) },
                    { CDF3( 2676,  7415, 11498) },
                    { CDF3(24678, 31958, 32585) },
                    { CDF3(18629, 29906, 31831) },
                    { CDF3( 9364, 20724, 26315) },
                    { CDF3( 4641, 12318, 18094) },
                    { CDF3( 2758,  7387, 11579) },
                    { CDF3(25433, 31842, 32469) },
                    { CDF3(18795, 29289, 31411) },
                    { CDF3( 7644, 17584, 23592) },
                    { CDF3( 3408,  9014, 15047) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 4536, 10072, 14001) },
                    { CDF3(25459, 31416, 32206) },
                    { CDF3(16605, 28048, 30818) },
                    { CDF3(11008, 22857, 27719) },
                    { CDF3( 6915, 16268, 22315) },
                    { CDF3( 2625,  6812, 10537) },
                    { CDF3(24257, 31788, 32499) },
                    { CDF3(16880, 29454, 31879) },
                    { CDF3(11958, 25054, 29778) },
                    { CDF3( 7916, 18718, 25084) },
                    { CDF3( 3383,  8777, 13446) },
                    { CDF3(22720, 31603, 32393) },
                    { CDF3(14960, 28125, 31335) },
                    { CDF3( 9731, 22210, 27928) },
                    { CDF3( 6304, 15832, 22277) },
                    { CDF3( 2910,  7818, 12166) },
                    { CDF3(20375, 30627, 32131) },
                    { CDF3(13904, 27284, 30887) },
                    { CDF3( 9368, 21558, 27144) },
                    { CDF3( 5937, 14966, 21119) },
                    { CDF3( 2667,  7225, 11319) },
                    { CDF3(23970, 31470, 32378) },
                    { CDF3(17173, 29734, 32018) },
                    { CDF3(12795, 25441, 29965) },
                    { CDF3( 8981, 19680, 25893) },
                    { CDF3( 4728, 11372, 16902) },
                    { CDF3(24287, 31797, 32439) },
                    { CDF3(16703, 29145, 31696) },
                    { CDF3(10833, 23554, 28725) },
                    { CDF3( 6468, 16566, 23057) },
                    { CDF3( 2415,  6562, 10278) },
                    { CDF3(26610, 32395, 32659) },
                    { CDF3(18590, 30498, 32117) },
                    { CDF3(12420, 25756, 29950) },
                    { CDF3( 7639, 18746, 24710) },
                    { CDF3( 3001,  8086, 12347) },
                    { CDF3(25076, 32064, 32580) },
                    { CDF3(17946, 30128, 32028) },
                    { CDF3(12024, 24985, 29378) },
                    { CDF3( 7517, 18390, 24304) },
                    { CDF3( 3243,  8781, 13331) },
                }, {
                    { CDF3( 6037, 16771, 21957) },
                    { CDF3(24774, 31704, 32426) },
                    { CDF3(16830, 28589, 31056) },
                    { CDF3(10602, 22828, 27760) },
                    { CDF3( 6733, 16829, 23071) },
                    { CDF3( 3250,  8914, 13556) },
                    { CDF3(25582, 32220, 32668) },
                    { CDF3(18659, 30342, 32223) },
                    { CDF3(12546, 26149, 30515) },
                    { CDF3( 8420, 20451, 26801) },
                    { CDF3( 4636, 12420, 18344) },
                    { CDF3(27581, 32362, 32639) },
                    { CDF3(18987, 30083, 31978) },
                    { CDF3(11327, 24248, 29084) },
                    { CDF3( 7264, 17719, 24120) },
                    { CDF3( 3995, 10768, 16169) },
                    { CDF3(25893, 31831, 32487) },
                    { CDF3(16577, 28587, 31379) },
                    { CDF3(10189, 22748, 28182) },
                    { CDF3( 6832, 17094, 23556) },
                    { CDF3( 3708, 10110, 15334) },
                    { CDF3(25904, 32282, 32656) },
                    { CDF3(19721, 30792, 32276) },
                    { CDF3(12819, 26243, 30411) },
                    { CDF3( 8572, 20614, 26891) },
                    { CDF3( 5364, 14059, 20467) },
                    { CDF3(26580, 32438, 32677) },
                    { CDF3(20852, 31225, 32340) },
                    { CDF3(12435, 25700, 29967) },
                    { CDF3( 8691, 20825, 26976) },
                    { CDF3( 4446, 12209, 17269) },
                    { CDF3(27350, 32429, 32696) },
                    { CDF3(21372, 30977, 32272) },
                    { CDF3(12673, 25270, 29853) },
                    { CDF3( 9208, 20925, 26640) },
                    { CDF3( 5018, 13351, 18732) },
                    { CDF3(27351, 32479, 32713) },
                    { CDF3(21398, 31209, 32387) },
                    { CDF3(12162, 25047, 29842) },
                    { CDF3( 7896, 18691, 25319) },
                    { CDF3( 4670, 12882, 18881) },
                },
            }, {
                {
                    { CDF3( 5487, 10460, 13708) },
                    { CDF3(21597, 28303, 30674) },
                    { CDF3(11037, 21953, 26476) },
                    { CDF3( 8147, 17962, 22952) },
                    { CDF3( 5242, 13061, 18532) },
                    { CDF3( 1889,  5208,  8182) },
                    { CDF3(26774, 32133, 32590) },
                    { CDF3(17844, 29564, 31767) },
                    { CDF3(11690, 24438, 29171) },
                    { CDF3( 7542, 18215, 24459) },
                    { CDF3( 2993,  8050, 12319) },
                    { CDF3(28023, 32328, 32591) },
                    { CDF3(18651, 30126, 31954) },
                    { CDF3(12164, 25146, 29589) },
                    { CDF3( 7762, 18530, 24771) },
                    { CDF3( 3492,  9183, 13920) },
                    { CDF3(27591, 32008, 32491) },
                    { CDF3(17149, 28853, 31510) },
                    { CDF3(11485, 24003, 28860) },
                    { CDF3( 7697, 18086, 24210) },
                    { CDF3( 3075,  7999, 12218) },
                    { CDF3(28268, 32482, 32654) },
                    { CDF3(19631, 31051, 32404) },
                    { CDF3(13860, 27260, 31020) },
                    { CDF3( 9605, 21613, 27594) },
                    { CDF3( 4876, 12162, 17908) },
                    { CDF3(27248, 32316, 32576) },
                    { CDF3(18955, 30457, 32075) },
                    { CDF3(11824, 23997, 28795) },
                    { CDF3( 7346, 18196, 24647) },
                    { CDF3( 3403,  9247, 14111) },
                    { CDF3(29711, 32655, 32735) },
                    { CDF3(21169, 31394, 32417) },
                    { CDF3(13487, 27198, 30957) },
                    { CDF3( 8828, 21683, 27614) },
                    { CDF3( 4270, 11451, 17038) },
                    { CDF3(28708, 32578, 32731) },
                    { CDF3(20120, 31241, 32482) },
                    { CDF3(13692, 27550, 31321) },
                    { CDF3( 9418, 22514, 28439) },
                    { CDF3( 4999, 13283, 19462) },
                }, {
                    { CDF3( 5673, 14302, 19711) },
                    { CDF3(26251, 30701, 31834) },
                    { CDF3(12782, 23783, 27803) },
                    { CDF3( 9127, 20657, 25808) },
                    { CDF3( 6368, 16208, 21462) },
                    { CDF3( 2465,  7177, 10822) },
                    { CDF3(29961, 32563, 32719) },
                    { CDF3(18318, 29891, 31949) },
                    { CDF3(11361, 24514, 29357) },
                    { CDF3( 7900, 19603, 25607) },
                    { CDF3( 4002, 10590, 15546) },
                    { CDF3(29637, 32310, 32595) },
                    { CDF3(18296, 29913, 31809) },
                    { CDF3(10144, 21515, 26871) },
                    { CDF3( 5358, 14322, 20394) },
                    { CDF3( 3067,  8362, 13346) },
                    { CDF3(28652, 32470, 32676) },
                    { CDF3(17538, 30771, 32209) },
                    { CDF3(13924, 26882, 30494) },
                    { CDF3(10496, 22837, 27869) },
                    { CDF3( 7236, 16396, 21621) },
                    { CDF3(30743, 32687, 32746) },
                    { CDF3(23006, 31676, 32489) },
                    { CDF3(14494, 27828, 31120) },
                    { CDF3(10174, 22801, 28352) },
                    { CDF3( 6242, 15281, 21043) },
                    { CDF3(25817, 32243, 32720) },
                    { CDF3(18618, 31367, 32325) },
                    { CDF3(13997, 28318, 31878) },
                    { CDF3(12255, 26534, 31383) },
                    { CDF3( 9561, 21588, 28450) },
                    { CDF3(28188, 32635, 32724) },
                    { CDF3(22060, 32365, 32728) },
                    { CDF3(18102, 30690, 32528) },
                    { CDF3(14196, 28864, 31999) },
                    { CDF3(12262, 25792, 30865) },
                    { CDF3(24176, 32109, 32628) },
                    { CDF3(18280, 29681, 31963) },
                    { CDF3(10205, 23703, 29664) },
                    { CDF3( 7889, 20025, 27676) },
                    { CDF3( 6060, 16743, 23970) },
                },
            }, {
                {
                    { CDF3( 5141,  7096,  8260) },
                    { CDF3(27186, 29022, 29789) },
                    { CDF3( 6668, 12568, 15682) },
                    { CDF3( 2172,  6181,  8638) },
                    { CDF3( 1126,  3379,  4531) },
                    { CDF3(  443,  1361,  2254) },
                    { CDF3(26083, 31153, 32436) },
                    { CDF3(13486, 24603, 28483) },
                    { CDF3( 6508, 14840, 19910) },
                    { CDF3( 3386,  8800, 13286) },
                    { CDF3( 1530,  4322,  7054) },
                    { CDF3(29639, 32080, 32548) },
                    { CDF3(15897, 27552, 30290) },
                    { CDF3( 8588, 20047, 25383) },
                    { CDF3( 4889, 13339, 19269) },
                    { CDF3( 2240,  6871, 10498) },
                    { CDF3(28165, 32197, 32517) },
                    { CDF3(20735, 30427, 31568) },
                    { CDF3(14325, 24671, 27692) },
                    { CDF3( 5119, 12554, 17805) },
                    { CDF3( 1810,  5441,  8261) },
                    { CDF3(31212, 32724, 32748) },
                    { CDF3(23352, 31766, 32545) },
                    { CDF3(14669, 27570, 31059) },
                    { CDF3( 8492, 20894, 27272) },
                    { CDF3( 3644, 10194, 15204) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 2461,  7013,  9371) },
                    { CDF3(24749, 29600, 30986) },
                    { CDF3( 9466, 19037, 22417) },
                    { CDF3( 3584,  9280, 14400) },
                    { CDF3( 1505,  3929,  5433) },
                    { CDF3(  677,  1500,  2736) },
                    { CDF3(23987, 30702, 32117) },
                    { CDF3(13554, 24571, 29263) },
                    { CDF3( 6211, 14556, 21155) },
                    { CDF3( 3135, 10972, 15625) },
                    { CDF3( 2435,  7127, 11427) },
                    { CDF3(31300, 32532, 32550) },
                    { CDF3(14757, 30365, 31954) },
                    { CDF3( 4405, 11612, 18553) },
                    { CDF3(  580,  4132,  7322) },
                    { CDF3( 1695, 10169, 14124) },
                    { CDF3(30008, 32282, 32591) },
                    { CDF3(19244, 30108, 31748) },
                    { CDF3(11180, 24158, 29555) },
                    { CDF3( 5650, 14972, 19209) },
                    { CDF3( 2114,  5109,  8456) },
                    { CDF3(31856, 32716, 32748) },
                    { CDF3(23012, 31664, 32572) },
                    { CDF3(13694, 26656, 30636) },
                    { CDF3( 8142, 19508, 26093) },
                    { CDF3( 4253, 10955, 16724) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3(  601,   983,  1311) },
                    { CDF3(18725, 23406, 28087) },
                    { CDF3( 5461,  8192, 10923) },
                    { CDF3( 3781, 15124, 21425) },
                    { CDF3( 2587,  7761, 12072) },
                    { CDF3(  106,   458,   810) },
                    { CDF3(22282, 29710, 31894) },
                    { CDF3( 8508, 20926, 25984) },
                    { CDF3( 3726, 12713, 18083) },
                    { CDF3( 1620,  7112, 10893) },
                    { CDF3(  729,  2236,  3495) },
                    { CDF3(30163, 32474, 32684) },
                    { CDF3(18304, 30464, 32000) },
                    { CDF3(11443, 26526, 29647) },
                    { CDF3( 6007, 15292, 21299) },
                    { CDF3( 2234,  6703,  8937) },
                    { CDF3(30954, 32177, 32571) },
                    { CDF3(17363, 29562, 31076) },
                    { CDF3( 9686, 22464, 27410) },
                    { CDF3( 8192, 16384, 21390) },
                    { CDF3( 1755,  8046, 11264) },
                    { CDF3(31168, 32734, 32748) },
                    { CDF3(22486, 31441, 32471) },
                    { CDF3(12833, 25627, 29738) },
                    { CDF3( 6980, 17379, 23122) },
                    { CDF3( 3111,  8887, 13479) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            },
        }, .dc_sign = {
            { { CDF1(16000) }, { CDF1(13056) }, { CDF1(18816) } },
            { { CDF1(15232) }, { CDF1(12928) }, { CDF1(17280) } },
        }, .br_tok = {
            {
                {
                    { CDF3(14298, 20718, 24174) },
                    { CDF3(12536, 19601, 23789) },
                    { CDF3( 8712, 15051, 19503) },
                    { CDF3( 6170, 11327, 15434) },
                    { CDF3( 4742,  8926, 12538) },
                    { CDF3( 3803,  7317, 10546) },
                    { CDF3( 1696,  3317,  4871) },
                    { CDF3(14392, 19951, 22756) },
                    { CDF3(15978, 23218, 26818) },
                    { CDF3(12187, 19474, 23889) },
                    { CDF3( 9176, 15640, 20259) },
                    { CDF3( 7068, 12655, 17028) },
                    { CDF3( 5656, 10442, 14472) },
                    { CDF3( 2580,  4992,  7244) },
                    { CDF3(12136, 18049, 21426) },
                    { CDF3(13784, 20721, 24481) },
                    { CDF3(10836, 17621, 21900) },
                    { CDF3( 8372, 14444, 18847) },
                    { CDF3( 6523, 11779, 16000) },
                    { CDF3( 5337,  9898, 13760) },
                    { CDF3( 3034,  5860,  8462) },
                }, {
                    { CDF3(15967, 22905, 26286) },
                    { CDF3(13534, 20654, 24579) },
                    { CDF3( 9504, 16092, 20535) },
                    { CDF3( 6975, 12568, 16903) },
                    { CDF3( 5364, 10091, 14020) },
                    { CDF3( 4357,  8370, 11857) },
                    { CDF3( 2506,  4934,  7218) },
                    { CDF3(23032, 28815, 30936) },
                    { CDF3(19540, 26704, 29719) },
                    { CDF3(15158, 22969, 27097) },
                    { CDF3(11408, 18865, 23650) },
                    { CDF3( 8885, 15448, 20250) },
                    { CDF3( 7108, 12853, 17416) },
                    { CDF3( 4231,  8041, 11480) },
                    { CDF3(19823, 26490, 29156) },
                    { CDF3(18890, 25929, 28932) },
                    { CDF3(15660, 23491, 27433) },
                    { CDF3(12147, 19776, 24488) },
                    { CDF3( 9728, 16774, 21649) },
                    { CDF3( 7919, 14277, 19066) },
                    { CDF3( 5440, 10170, 14185) },
                },
            }, {
                {
                    { CDF3(14406, 20862, 24414) },
                    { CDF3(11824, 18907, 23109) },
                    { CDF3( 8257, 14393, 18803) },
                    { CDF3( 5860, 10747, 14778) },
                    { CDF3( 4475,  8486, 11984) },
                    { CDF3( 3606,  6954, 10043) },
                    { CDF3( 1736,  3410,  5048) },
                    { CDF3(14430, 20046, 22882) },
                    { CDF3(15593, 22899, 26709) },
                    { CDF3(12102, 19368, 23811) },
                    { CDF3( 9059, 15584, 20262) },
                    { CDF3( 6999, 12603, 17048) },
                    { CDF3( 5684, 10497, 14553) },
                    { CDF3( 2822,  5438,  7862) },
                    { CDF3(15785, 21585, 24359) },
                    { CDF3(18347, 25229, 28266) },
                    { CDF3(14974, 22487, 26389) },
                    { CDF3(11423, 18681, 23271) },
                    { CDF3( 8863, 15350, 20008) },
                    { CDF3( 7153, 12852, 17278) },
                    { CDF3( 3707,  7036,  9982) },
                }, {
                    { CDF3(15460, 21696, 25469) },
                    { CDF3(12170, 19249, 23191) },
                    { CDF3( 8723, 15027, 19332) },
                    { CDF3( 6428, 11704, 15874) },
                    { CDF3( 4922,  9292, 13052) },
                    { CDF3( 4139,  7695, 11010) },
                    { CDF3( 2291,  4508,  6598) },
                    { CDF3(19856, 26920, 29828) },
                    { CDF3(17923, 25289, 28792) },
                    { CDF3(14278, 21968, 26297) },
                    { CDF3(10910, 18136, 22950) },
                    { CDF3( 8423, 14815, 19627) },
                    { CDF3( 6771, 12283, 16774) },
                    { CDF3( 4074,  7750, 11081) },
                    { CDF3(19852, 26074, 28672) },
                    { CDF3(19371, 26110, 28989) },
                    { CDF3(16265, 23873, 27663) },
                    { CDF3(12758, 20378, 24952) },
                    { CDF3(10095, 17098, 21961) },
                    { CDF3( 8250, 14628, 19451) },
                    { CDF3( 5205,  9745, 13622) },
                },
            }, {
                {
                    { CDF3(10563, 16233, 19763) },
                    { CDF3( 9794, 16022, 19804) },
                    { CDF3( 6750, 11945, 15759) },
                    { CDF3( 4963,  9186, 12752) },
                    { CDF3( 3845,  7435, 10627) },
                    { CDF3( 3051,  6085,  8834) },
                    { CDF3( 1311,  2596,  3830) },
                    { CDF3(11246, 16404, 19689) },
                    { CDF3(12315, 18911, 22731) },
                    { CDF3(10557, 17095, 21289) },
                    { CDF3( 8136, 14006, 18249) },
                    { CDF3( 6348, 11474, 15565) },
                    { CDF3( 5196,  9655, 13400) },
                    { CDF3( 2349,  4526,  6587) },
                    { CDF3(13337, 18730, 21569) },
                    { CDF3(19306, 26071, 28882) },
                    { CDF3(15952, 23540, 27254) },
                    { CDF3(12409, 19934, 24430) },
                    { CDF3( 9760, 16706, 21389) },
                    { CDF3( 8004, 14220, 18818) },
                    { CDF3( 4138,  7794, 10961) },
                }, {
                    { CDF3(10870, 16684, 20949) },
                    { CDF3( 9664, 15230, 18680) },
                    { CDF3( 6886, 12109, 15408) },
                    { CDF3( 4825,  8900, 12305) },
                    { CDF3( 3630,  7162, 10314) },
                    { CDF3( 3036,  6429,  9387) },
                    { CDF3( 1671,  3296,  4940) },
                    { CDF3(13819, 19159, 23026) },
                    { CDF3(11984, 19108, 23120) },
                    { CDF3(10690, 17210, 21663) },
                    { CDF3( 7984, 14154, 18333) },
                    { CDF3( 6868, 12294, 16124) },
                    { CDF3( 5274,  8994, 12868) },
                    { CDF3( 2988,  5771,  8424) },
                    { CDF3(19736, 26647, 29141) },
                    { CDF3(18933, 26070, 28984) },
                    { CDF3(15779, 23048, 27200) },
                    { CDF3(12638, 20061, 24532) },
                    { CDF3(10692, 17545, 22220) },
                    { CDF3( 9217, 15251, 20054) },
                    { CDF3( 5078,  9284, 12594) },
                },
            }, {
                {
                    { CDF3( 2331,  3662,  5244) },
                    { CDF3( 2891,  4771,  6145) },
                    { CDF3( 4598,  7623,  9729) },
                    { CDF3( 3520,  6845,  9199) },
                    { CDF3( 3417,  6119,  9324) },
                    { CDF3( 2601,  5412,  7385) },
                    { CDF3(  600,  1173,  1744) },
                    { CDF3( 7672, 13286, 17469) },
                    { CDF3( 4232,  7792, 10793) },
                    { CDF3( 2915,  5317,  7397) },
                    { CDF3( 2318,  4356,  6152) },
                    { CDF3( 2127,  4000,  5554) },
                    { CDF3( 1850,  3478,  5275) },
                    { CDF3(  977,  1933,  2843) },
                    { CDF3(18280, 24387, 27989) },
                    { CDF3(15852, 22671, 26185) },
                    { CDF3(13845, 20951, 24789) },
                    { CDF3(11055, 17966, 22129) },
                    { CDF3( 9138, 15422, 19801) },
                    { CDF3( 7454, 13145, 17456) },
                    { CDF3( 3370,  6393,  9013) },
                }, {
                    { CDF3( 5842,  9229, 10838) },
                    { CDF3( 2313,  3491,  4276) },
                    { CDF3( 2998,  6104,  7496) },
                    { CDF3( 2420,  7447,  9868) },
                    { CDF3( 3034,  8495, 10923) },
                    { CDF3( 4076,  8937, 10975) },
                    { CDF3( 1086,  2370,  3299) },
                    { CDF3( 9714, 17254, 20444) },
                    { CDF3( 8543, 13698, 17123) },
                    { CDF3( 4918,  9007, 11910) },
                    { CDF3( 4129,  7532, 10553) },
                    { CDF3( 2364,  5533,  8058) },
                    { CDF3( 1834,  3546,  5563) },
                    { CDF3( 1473,  2908,  4133) },
                    { CDF3(15405, 21193, 25619) },
                    { CDF3(15691, 21952, 26561) },
                    { CDF3(12962, 19194, 24165) },
                    { CDF3(10272, 17855, 22129) },
                    { CDF3( 8588, 15270, 20718) },
                    { CDF3( 8682, 14669, 19500) },
                    { CDF3( 4870,  9636, 13205) },
                },
            },
        },
    }, [1] = {
        .skip = {
            {
                { CDF1(30371) }, { CDF1( 7570) }, { CDF1(13155) },
                { CDF1(20751) }, { CDF1(20969) }, { CDF1(27067) },
                { CDF1(32013) }, { CDF1( 5495) }, { CDF1(17942) },
                { CDF1(28280) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(31782) }, { CDF1( 1836) }, { CDF1(10689) },
                { CDF1(17604) }, { CDF1(21622) }, { CDF1(27518) },
                { CDF1(32399) }, { CDF1( 4419) }, { CDF1(16294) },
                { CDF1(28345) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(31901) }, { CDF1(10311) }, { CDF1(18047) },
                { CDF1(24806) }, { CDF1(23288) }, { CDF1(27914) },
                { CDF1(32296) }, { CDF1( 4215) }, { CDF1(15756) },
                { CDF1(28341) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(26726) }, { CDF1( 1045) }, { CDF1(11703) },
                { CDF1(20590) }, { CDF1(18554) }, { CDF1(25970) },
                { CDF1(31938) }, { CDF1( 5583) }, { CDF1(21313) },
                { CDF1(29390) }, { CDF1(  641) }, { CDF1(22265) },
                { CDF1(31452) },
            }, {
                { CDF1(26584) }, { CDF1(  188) }, { CDF1( 8847) },
                { CDF1(24519) }, { CDF1(22938) }, { CDF1(30583) },
                { CDF1(32608) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            },
        }, .eob_bin_16 = {
            {
                { CDF4( 2125,  2551,  5165,  8946) },
                { CDF4(  513,   765,  1859,  6339) },
            }, {
                { CDF4( 7637,  9498, 14259, 19108) },
                { CDF4( 2497,  4096,  8866, 16993) },
            },
        }, .eob_bin_32 = {
            {
                { CDF5(  989,  1249,  2019,  4151, 10785) },
                { CDF5(  313,   441,  1099,  2917,  8562) },
            }, {
                { CDF5( 8394, 10352, 13932, 18855, 26014) },
                { CDF5( 2578,  4124,  8181, 13670, 24234) },
            },
        }, .eob_bin_64 = {
            {
                { CDF6( 1260,  1446,  2253,  3712,  6652, 13369) },
                { CDF6(  401,   605,  1029,  2563,  5845, 12626) },
            }, {
                { CDF6( 8609, 10612, 14624, 18714, 22614, 29024) },
                { CDF6( 1923,  3127,  5867,  9703, 14277, 27100) },
            },
        }, .eob_bin_128 = {
            {
                { CDF7(  685,   933,  1488,  2714,  4766,  8562, 19254) },
                { CDF7(  217,   352,   618,  2303,  5261,  9969, 17472) },
            }, {
                { CDF7( 8045, 11200, 15497, 19595, 23948, 27408, 30938) },
                { CDF7( 2310,  4160,  7471, 14997, 17931, 20768, 30240) },
            },
        }, .eob_bin_256 = {
            {
                { CDF8( 1448,  2109,  4151,  6263,
                        9329, 13260, 17944, 23300) },
                { CDF8(  399,  1019,  1749,  3038,
                       10444, 15546, 22739, 27294) },
            }, {
                { CDF8( 6402,  8148, 12623, 15072,
                       18728, 22847, 26447, 29377) },
                { CDF8( 1674,  3252,  5734, 10159,
                       22397, 23802, 24821, 30940) },
            },
        }, .eob_bin_512 = {
            { CDF9( 1230,  2278,  5035,  7776, 11871,
                   15346, 19590, 24584, 28749) },
            { CDF9( 7265,  9979, 15819, 19250, 21780,
                   23846, 26478, 28396, 31811) },
        }, .eob_bin_1024 = {
            { CDF10(  696,   948,  3145,  5702,  9706,
                    13217, 17851, 21856, 25692, 28034) },
            { CDF10( 2672,  3591,  9330, 17084, 22725,
                    24284, 26527, 28027, 28377, 30876) },
        }, .eob_hi_bit = {
            {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(17471) },
                    { CDF1(20223) }, { CDF1(11357) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20335) },
                    { CDF1(21667) }, { CDF1(14818) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20430) },
                    { CDF1(20662) }, { CDF1(15367) }, { CDF1(16970) },
                    { CDF1(14657) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(22117) },
                    { CDF1(22028) }, { CDF1(18650) }, { CDF1(16042) },
                    { CDF1(15885) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(22409) },
                    { CDF1(21012) }, { CDF1(15650) }, { CDF1(17395) },
                    { CDF1(15469) }, { CDF1(20205) }, { CDF1(19511) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(24220) },
                    { CDF1(22480) }, { CDF1(17737) }, { CDF1(18916) },
                    { CDF1(19268) }, { CDF1(18412) }, { CDF1(18844) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(25991) },
                    { CDF1(20314) }, { CDF1(17731) }, { CDF1(19678) },
                    { CDF1(18649) }, { CDF1(17307) }, { CDF1(21798) },
                    { CDF1(17549) }, { CDF1(15630) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(26585) },
                    { CDF1(21469) }, { CDF1(20432) }, { CDF1(17735) },
                    { CDF1(19280) }, { CDF1(15235) }, { CDF1(20297) },
                    { CDF1(22471) }, { CDF1(28997) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(26605) },
                    { CDF1(11304) }, { CDF1(16726) }, { CDF1(16560) },
                    { CDF1(20866) }, { CDF1(23524) }, { CDF1(19878) },
                    { CDF1(13469) }, { CDF1(23084) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            },
        }, .eob_base_tok = {
            {
                {
                    { CDF2(17560, 29888) }, { CDF2(29671, 31549) },
                    { CDF2(31007, 32056) }, { CDF2(27286, 30006) },
                }, {
                    { CDF2(26594, 31212) }, { CDF2(31208, 32582) },
                    { CDF2(31835, 32637) }, { CDF2(30595, 32206) },
                },
            }, {
                {
                    { CDF2(15239, 29932) }, { CDF2(31315, 32095) },
                    { CDF2(32130, 32434) }, { CDF2(30864, 31996) },
                }, {
                    { CDF2(26279, 30968) }, { CDF2(31142, 32495) },
                    { CDF2(31713, 32540) }, { CDF2(31929, 32594) },
                },
            }, {
                {
                    { CDF2( 2644, 25198) }, { CDF2(32038, 32451) },
                    { CDF2(32639, 32695) }, { CDF2(32166, 32518) },
                }, {
                    { CDF2(17187, 27668) }, { CDF2(31714, 32550) },
                    { CDF2(32283, 32678) }, { CDF2(31930, 32563) },
                },
            }, {
                {
                    { CDF2( 1044,  2257) }, { CDF2(30755, 31923) },
                    { CDF2(32208, 32693) }, { CDF2(32244, 32615) },
                }, {
                    { CDF2(21317, 26207) }, { CDF2(29133, 30868) },
                    { CDF2(29311, 31231) }, { CDF2(29657, 31087) },
                },
            }, {
                {
                    { CDF2(  478,  1834) }, { CDF2(31005, 31987) },
                    { CDF2(32317, 32724) }, { CDF2(30865, 32648) },
                }, {
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                },
            },
        }, .base_tok = {
            {
                {
                    { CDF3( 6041, 11854, 15927) },
                    { CDF3(20326, 30905, 32251) },
                    { CDF3(14164, 26831, 30725) },
                    { CDF3( 9760, 20647, 26585) },
                    { CDF3( 6416, 14953, 21219) },
                    { CDF3( 2966,  7151, 10891) },
                    { CDF3(23567, 31374, 32254) },
                    { CDF3(14978, 27416, 30946) },
                    { CDF3( 9434, 20225, 26254) },
                    { CDF3( 6658, 14558, 20535) },
                    { CDF3( 3916,  8677, 12989) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(18088, 29545, 31587) },
                    { CDF3(13062, 25843, 30073) },
                    { CDF3( 8940, 16827, 22251) },
                    { CDF3( 7654, 13220, 17973) },
                    { CDF3( 5733, 10316, 14456) },
                    { CDF3(22879, 31388, 32114) },
                    { CDF3(15215, 27993, 30955) },
                    { CDF3( 9397, 19445, 24978) },
                    { CDF3( 3442,  9813, 15344) },
                    { CDF3( 1368,  3936,  6532) },
                    { CDF3(25494, 32033, 32406) },
                    { CDF3(16772, 27963, 30718) },
                    { CDF3( 9419, 18165, 23260) },
                    { CDF3( 2677,  7501, 11797) },
                    { CDF3( 1516,  4344,  7170) },
                    { CDF3(26556, 31454, 32101) },
                    { CDF3(17128, 27035, 30108) },
                    { CDF3( 8324, 15344, 20249) },
                    { CDF3( 1903,  5696,  9469) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 8455, 19003, 24368) },
                    { CDF3(23563, 32021, 32604) },
                    { CDF3(16237, 29446, 31935) },
                    { CDF3(10724, 23999, 29358) },
                    { CDF3( 6725, 17528, 24416) },
                    { CDF3( 3927, 10927, 16825) },
                    { CDF3(26313, 32288, 32634) },
                    { CDF3(17430, 30095, 32095) },
                    { CDF3(11116, 24606, 29679) },
                    { CDF3( 7195, 18384, 25269) },
                    { CDF3( 4726, 12852, 19315) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(22822, 31648, 32483) },
                    { CDF3(16724, 29633, 31929) },
                    { CDF3(10261, 23033, 28725) },
                    { CDF3( 7029, 17840, 24528) },
                    { CDF3( 4867, 13886, 21502) },
                    { CDF3(25298, 31892, 32491) },
                    { CDF3(17809, 29330, 31512) },
                    { CDF3( 9668, 21329, 26579) },
                    { CDF3( 4774, 12956, 18976) },
                    { CDF3( 2322,  7030, 11540) },
                    { CDF3(25472, 31920, 32543) },
                    { CDF3(17957, 29387, 31632) },
                    { CDF3( 9196, 20593, 26400) },
                    { CDF3( 4680, 12705, 19202) },
                    { CDF3( 2917,  8456, 13436) },
                    { CDF3(26471, 32059, 32574) },
                    { CDF3(18458, 29783, 31909) },
                    { CDF3( 8400, 19464, 25956) },
                    { CDF3( 3812, 10973, 17206) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 6779, 13743, 17678) },
                    { CDF3(24806, 31797, 32457) },
                    { CDF3(17616, 29047, 31372) },
                    { CDF3(11063, 23175, 28003) },
                    { CDF3( 6521, 16110, 22324) },
                    { CDF3( 2764,  7504, 11654) },
                    { CDF3(25266, 32367, 32637) },
                    { CDF3(19054, 30553, 32175) },
                    { CDF3(12139, 25212, 29807) },
                    { CDF3( 7311, 18162, 24704) },
                    { CDF3( 3397,  9164, 14074) },
                    { CDF3(25988, 32208, 32522) },
                    { CDF3(16253, 28912, 31526) },
                    { CDF3( 9151, 21387, 27372) },
                    { CDF3( 5688, 14915, 21496) },
                    { CDF3( 2717,  7627, 12004) },
                    { CDF3(23144, 31855, 32443) },
                    { CDF3(16070, 28491, 31325) },
                    { CDF3( 8702, 20467, 26517) },
                    { CDF3( 5243, 13956, 20367) },
                    { CDF3( 2621,  7335, 11567) },
                    { CDF3(26636, 32340, 32630) },
                    { CDF3(19990, 31050, 32341) },
                    { CDF3(13243, 26105, 30315) },
                    { CDF3( 8588, 19521, 25918) },
                    { CDF3( 4717, 11585, 17304) },
                    { CDF3(25844, 32292, 32582) },
                    { CDF3(19090, 30635, 32097) },
                    { CDF3(11963, 24546, 28939) },
                    { CDF3( 6218, 16087, 22354) },
                    { CDF3( 2340,  6608, 10426) },
                    { CDF3(28046, 32576, 32694) },
                    { CDF3(21178, 31313, 32296) },
                    { CDF3(13486, 26184, 29870) },
                    { CDF3( 7149, 17871, 23723) },
                    { CDF3( 2833,  7958, 12259) },
                    { CDF3(27710, 32528, 32686) },
                    { CDF3(20674, 31076, 32268) },
                    { CDF3(12413, 24955, 29243) },
                    { CDF3( 6676, 16927, 23097) },
                    { CDF3( 2966,  8333, 12919) },
                }, {
                    { CDF3( 8639, 19339, 24429) },
                    { CDF3(24404, 31837, 32525) },
                    { CDF3(16997, 29425, 31784) },
                    { CDF3(11253, 24234, 29149) },
                    { CDF3( 6751, 17394, 24028) },
                    { CDF3( 3490,  9830, 15191) },
                    { CDF3(26283, 32471, 32714) },
                    { CDF3(19599, 31168, 32442) },
                    { CDF3(13146, 26954, 30893) },
                    { CDF3( 8214, 20588, 26890) },
                    { CDF3( 4699, 13081, 19300) },
                    { CDF3(28212, 32458, 32669) },
                    { CDF3(18594, 30316, 32100) },
                    { CDF3(11219, 24408, 29234) },
                    { CDF3( 6865, 17656, 24149) },
                    { CDF3( 3678, 10362, 16006) },
                    { CDF3(25825, 32136, 32616) },
                    { CDF3(17313, 29853, 32021) },
                    { CDF3(11197, 24471, 29472) },
                    { CDF3( 6947, 17781, 24405) },
                    { CDF3( 3768, 10660, 16261) },
                    { CDF3(27352, 32500, 32706) },
                    { CDF3(20850, 31468, 32469) },
                    { CDF3(14021, 27707, 31133) },
                    { CDF3( 8964, 21748, 27838) },
                    { CDF3( 5437, 14665, 21187) },
                    { CDF3(26304, 32492, 32698) },
                    { CDF3(20409, 31380, 32385) },
                    { CDF3(13682, 27222, 30632) },
                    { CDF3( 8974, 21236, 26685) },
                    { CDF3( 4234, 11665, 16934) },
                    { CDF3(26273, 32357, 32711) },
                    { CDF3(20672, 31242, 32441) },
                    { CDF3(14172, 27254, 30902) },
                    { CDF3( 9870, 21898, 27275) },
                    { CDF3( 5164, 13506, 19270) },
                    { CDF3(26725, 32459, 32728) },
                    { CDF3(20991, 31442, 32527) },
                    { CDF3(13071, 26434, 30811) },
                    { CDF3( 8184, 20090, 26742) },
                    { CDF3( 4803, 13255, 19895) },
                },
            }, {
                {
                    { CDF3( 7555, 14942, 18501) },
                    { CDF3(24410, 31178, 32287) },
                    { CDF3(14394, 26738, 30253) },
                    { CDF3( 8413, 19554, 25195) },
                    { CDF3( 4766, 12924, 18785) },
                    { CDF3( 2029,  5806,  9207) },
                    { CDF3(26776, 32364, 32663) },
                    { CDF3(18732, 29967, 31931) },
                    { CDF3(11005, 23786, 28852) },
                    { CDF3( 6466, 16909, 23510) },
                    { CDF3( 3044,  8638, 13419) },
                    { CDF3(29208, 32582, 32704) },
                    { CDF3(20068, 30857, 32208) },
                    { CDF3(12003, 25085, 29595) },
                    { CDF3( 6947, 17750, 24189) },
                    { CDF3( 3245,  9103, 14007) },
                    { CDF3(27359, 32465, 32669) },
                    { CDF3(19421, 30614, 32174) },
                    { CDF3(11915, 25010, 29579) },
                    { CDF3( 6950, 17676, 24074) },
                    { CDF3( 3007,  8473, 13096) },
                    { CDF3(29002, 32676, 32735) },
                    { CDF3(22102, 31849, 32576) },
                    { CDF3(14408, 28009, 31405) },
                    { CDF3( 9027, 21679, 27931) },
                    { CDF3( 4694, 12678, 18748) },
                    { CDF3(28216, 32528, 32682) },
                    { CDF3(20849, 31264, 32318) },
                    { CDF3(12756, 25815, 29751) },
                    { CDF3( 7565, 18801, 24923) },
                    { CDF3( 3509,  9533, 14477) },
                    { CDF3(30133, 32687, 32739) },
                    { CDF3(23063, 31910, 32515) },
                    { CDF3(14588, 28051, 31132) },
                    { CDF3( 9085, 21649, 27457) },
                    { CDF3( 4261, 11654, 17264) },
                    { CDF3(29518, 32691, 32748) },
                    { CDF3(22451, 31959, 32613) },
                    { CDF3(14864, 28722, 31700) },
                    { CDF3( 9695, 22964, 28716) },
                    { CDF3( 4932, 13358, 19502) },
                }, {
                    { CDF3( 6465, 16958, 21688) },
                    { CDF3(25199, 31514, 32360) },
                    { CDF3(14774, 27149, 30607) },
                    { CDF3( 9257, 21438, 26972) },
                    { CDF3( 5723, 15183, 21882) },
                    { CDF3( 3150,  8879, 13731) },
                    { CDF3(26989, 32262, 32682) },
                    { CDF3(17396, 29937, 32085) },
                    { CDF3(11387, 24901, 29784) },
                    { CDF3( 7289, 18821, 25548) },
                    { CDF3( 3734, 10577, 16086) },
                    { CDF3(29728, 32501, 32695) },
                    { CDF3(17431, 29701, 31903) },
                    { CDF3( 9921, 22826, 28300) },
                    { CDF3( 5896, 15434, 22068) },
                    { CDF3( 3430,  9646, 14757) },
                    { CDF3(28614, 32511, 32705) },
                    { CDF3(19364, 30638, 32263) },
                    { CDF3(13129, 26254, 30402) },
                    { CDF3( 8754, 20484, 26440) },
                    { CDF3( 4378, 11607, 17110) },
                    { CDF3(30292, 32671, 32744) },
                    { CDF3(21780, 31603, 32501) },
                    { CDF3(14314, 27829, 31291) },
                    { CDF3( 9611, 22327, 28263) },
                    { CDF3( 4890, 13087, 19065) },
                    { CDF3(25862, 32567, 32733) },
                    { CDF3(20794, 32050, 32567) },
                    { CDF3(17243, 30625, 32254) },
                    { CDF3(13283, 27628, 31474) },
                    { CDF3( 9669, 22532, 28918) },
                    { CDF3(27435, 32697, 32748) },
                    { CDF3(24922, 32390, 32714) },
                    { CDF3(21449, 31504, 32536) },
                    { CDF3(16392, 29729, 31832) },
                    { CDF3(11692, 24884, 29076) },
                    { CDF3(24193, 32290, 32735) },
                    { CDF3(18909, 31104, 32563) },
                    { CDF3(12236, 26841, 31403) },
                    { CDF3( 8171, 21840, 29082) },
                    { CDF3( 7224, 17280, 25275) },
                },
            }, {
                {
                    { CDF3( 3078,  6839,  9890) },
                    { CDF3(13837, 20450, 24479) },
                    { CDF3( 5914, 14222, 19328) },
                    { CDF3( 3866, 10267, 14762) },
                    { CDF3( 2612,  7208, 11042) },
                    { CDF3( 1067,  2991,  4776) },
                    { CDF3(25817, 31646, 32529) },
                    { CDF3(13708, 26338, 30385) },
                    { CDF3( 7328, 18585, 24870) },
                    { CDF3( 4691, 13080, 19276) },
                    { CDF3( 1825,  5253,  8352) },
                    { CDF3(29386, 32315, 32624) },
                    { CDF3(17160, 29001, 31360) },
                    { CDF3( 9602, 21862, 27396) },
                    { CDF3( 5915, 15772, 22148) },
                    { CDF3( 2786,  7779, 12047) },
                    { CDF3(29246, 32450, 32663) },
                    { CDF3(18696, 29929, 31818) },
                    { CDF3(10510, 23369, 28560) },
                    { CDF3( 6229, 16499, 23125) },
                    { CDF3( 2608,  7448, 11705) },
                    { CDF3(30753, 32710, 32748) },
                    { CDF3(21638, 31487, 32503) },
                    { CDF3(12937, 26854, 30870) },
                    { CDF3( 8182, 20596, 26970) },
                    { CDF3( 3637, 10269, 15497) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 5244, 12150, 16906) },
                    { CDF3(20486, 26858, 29701) },
                    { CDF3( 7756, 18317, 23735) },
                    { CDF3( 3452,  9256, 13146) },
                    { CDF3( 2020,  5206,  8229) },
                    { CDF3( 1801,  4993,  7903) },
                    { CDF3(27051, 31858, 32531) },
                    { CDF3(15988, 27531, 30619) },
                    { CDF3( 9188, 21484, 26719) },
                    { CDF3( 6273, 17186, 23800) },
                    { CDF3( 3108,  9355, 14764) },
                    { CDF3(31076, 32520, 32680) },
                    { CDF3(18119, 30037, 31850) },
                    { CDF3(10244, 22969, 27472) },
                    { CDF3( 4692, 14077, 19273) },
                    { CDF3( 3694, 11677, 17556) },
                    { CDF3(30060, 32581, 32720) },
                    { CDF3(21011, 30775, 32120) },
                    { CDF3(11931, 24820, 29289) },
                    { CDF3( 7119, 17662, 24356) },
                    { CDF3( 3833, 10706, 16304) },
                    { CDF3(31954, 32731, 32748) },
                    { CDF3(23913, 31724, 32489) },
                    { CDF3(15520, 28060, 31286) },
                    { CDF3(11517, 23008, 28571) },
                    { CDF3( 6193, 14508, 20629) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 1035,  2807,  4156) },
                    { CDF3(13162, 18138, 20939) },
                    { CDF3( 2696,  6633,  8755) },
                    { CDF3( 1373,  4161,  6853) },
                    { CDF3( 1099,  2746,  4716) },
                    { CDF3(  340,  1021,  1599) },
                    { CDF3(22826, 30419, 32135) },
                    { CDF3(10395, 21762, 26942) },
                    { CDF3( 4726, 12407, 17361) },
                    { CDF3( 2447,  7080, 10593) },
                    { CDF3( 1227,  3717,  6011) },
                    { CDF3(28156, 31424, 31934) },
                    { CDF3(16915, 27754, 30373) },
                    { CDF3( 9148, 20990, 26431) },
                    { CDF3( 5950, 15515, 21148) },
                    { CDF3( 2492,  7327, 11526) },
                    { CDF3(30602, 32477, 32670) },
                    { CDF3(20026, 29955, 31568) },
                    { CDF3(11220, 23628, 28105) },
                    { CDF3( 6652, 17019, 22973) },
                    { CDF3( 3064,  8536, 13043) },
                    { CDF3(31769, 32724, 32748) },
                    { CDF3(22230, 30887, 32373) },
                    { CDF3(12234, 25079, 29731) },
                    { CDF3( 7326, 18816, 25353) },
                    { CDF3( 3933, 10907, 16616) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            },
        }, .dc_sign = {
            { { CDF1(16000) }, { CDF1(13056) }, { CDF1(18816) } },
            { { CDF1(15232) }, { CDF1(12928) }, { CDF1(17280) } },
        }, .br_tok = {
            {
                {
                    { CDF3(14995, 21341, 24749) },
                    { CDF3(13158, 20289, 24601) },
                    { CDF3( 8941, 15326, 19876) },
                    { CDF3( 6297, 11541, 15807) },
                    { CDF3( 4817,  9029, 12776) },
                    { CDF3( 3731,  7273, 10627) },
                    { CDF3( 1847,  3617,  5354) },
                    { CDF3(14472, 19659, 22343) },
                    { CDF3(16806, 24162, 27533) },
                    { CDF3(12900, 20404, 24713) },
                    { CDF3( 9411, 16112, 20797) },
                    { CDF3( 7056, 12697, 17148) },
                    { CDF3( 5544, 10339, 14460) },
                    { CDF3( 2954,  5704,  8319) },
                    { CDF3(12464, 18071, 21354) },
                    { CDF3(15482, 22528, 26034) },
                    { CDF3(12070, 19269, 23624) },
                    { CDF3( 8953, 15406, 20106) },
                    { CDF3( 7027, 12730, 17220) },
                    { CDF3( 5887, 10913, 15140) },
                    { CDF3( 3793,  7278, 10447) },
                }, {
                    { CDF3(15571, 22232, 25749) },
                    { CDF3(14506, 21575, 25374) },
                    { CDF3(10189, 17089, 21569) },
                    { CDF3( 7316, 13301, 17915) },
                    { CDF3( 5783, 10912, 15190) },
                    { CDF3( 4760,  9155, 13088) },
                    { CDF3( 2993,  5966,  8774) },
                    { CDF3(23424, 28903, 30778) },
                    { CDF3(20775, 27666, 30290) },
                    { CDF3(16474, 24410, 28299) },
                    { CDF3(12471, 20180, 24987) },
                    { CDF3( 9410, 16487, 21439) },
                    { CDF3( 7536, 13614, 18529) },
                    { CDF3( 5048,  9586, 13549) },
                    { CDF3(21090, 27290, 29756) },
                    { CDF3(20796, 27402, 30026) },
                    { CDF3(17819, 25485, 28969) },
                    { CDF3(13860, 21909, 26462) },
                    { CDF3(11002, 18494, 23529) },
                    { CDF3( 8953, 15929, 20897) },
                    { CDF3( 6448, 11918, 16454) },
                },
            }, {
                {
                    { CDF3(15999, 22208, 25449) },
                    { CDF3(13050, 19988, 24122) },
                    { CDF3( 8594, 14864, 19378) },
                    { CDF3( 6033, 11079, 15238) },
                    { CDF3( 4554,  8683, 12347) },
                    { CDF3( 3672,  7139, 10337) },
                    { CDF3( 1900,  3771,  5576) },
                    { CDF3(15788, 21340, 23949) },
                    { CDF3(16825, 24235, 27758) },
                    { CDF3(12873, 20402, 24810) },
                    { CDF3( 9590, 16363, 21094) },
                    { CDF3( 7352, 13209, 17733) },
                    { CDF3( 5960, 10989, 15184) },
                    { CDF3( 3232,  6234,  9007) },
                    { CDF3(15761, 20716, 23224) },
                    { CDF3(19318, 25989, 28759) },
                    { CDF3(15529, 23094, 26929) },
                    { CDF3(11662, 18989, 23641) },
                    { CDF3( 8955, 15568, 20366) },
                    { CDF3( 7281, 13106, 17708) },
                    { CDF3( 4248,  8059, 11440) },
                }, {
                    { CDF3(14899, 21217, 24503) },
                    { CDF3(13519, 20283, 24047) },
                    { CDF3( 9429, 15966, 20365) },
                    { CDF3( 6700, 12355, 16652) },
                    { CDF3( 5088,  9704, 13716) },
                    { CDF3( 4243,  8154, 11731) },
                    { CDF3( 2702,  5364,  7861) },
                    { CDF3(22745, 28388, 30454) },
                    { CDF3(20235, 27146, 29922) },
                    { CDF3(15896, 23715, 27637) },
                    { CDF3(11840, 19350, 24131) },
                    { CDF3( 9122, 15932, 20880) },
                    { CDF3( 7488, 13581, 18362) },
                    { CDF3( 5114,  9568, 13370) },
                    { CDF3(20845, 26553, 28932) },
                    { CDF3(20981, 27372, 29884) },
                    { CDF3(17781, 25335, 28785) },
                    { CDF3(13760, 21708, 26297) },
                    { CDF3(10975, 18415, 23365) },
                    { CDF3( 9045, 15789, 20686) },
                    { CDF3( 6130, 11199, 15423) },
                },
            }, {
                {
                    { CDF3(13549, 19724, 23158) },
                    { CDF3(11844, 18382, 22246) },
                    { CDF3( 7919, 13619, 17773) },
                    { CDF3( 5486, 10143, 13946) },
                    { CDF3( 4166,  7983, 11324) },
                    { CDF3( 3364,  6506,  9427) },
                    { CDF3( 1598,  3160,  4674) },
                    { CDF3(15281, 20979, 23781) },
                    { CDF3(14939, 22119, 25952) },
                    { CDF3(11363, 18407, 22812) },
                    { CDF3( 8609, 14857, 19370) },
                    { CDF3( 6737, 12184, 16480) },
                    { CDF3( 5506, 10263, 14262) },
                    { CDF3( 2990,  5786,  8380) },
                    { CDF3(20249, 25253, 27417) },
                    { CDF3(21070, 27518, 30001) },
                    { CDF3(16854, 24469, 28074) },
                    { CDF3(12864, 20486, 25000) },
                    { CDF3( 9962, 16978, 21778) },
                    { CDF3( 8074, 14338, 19048) },
                    { CDF3( 4494,  8479, 11906) },
                }, {
                    { CDF3(13960, 19617, 22829) },
                    { CDF3(11150, 17341, 21228) },
                    { CDF3( 7150, 12964, 17190) },
                    { CDF3( 5331, 10002, 13867) },
                    { CDF3( 4167,  7744, 11057) },
                    { CDF3( 3480,  6629,  9646) },
                    { CDF3( 1883,  3784,  5686) },
                    { CDF3(18752, 25660, 28912) },
                    { CDF3(16968, 24586, 28030) },
                    { CDF3(13520, 21055, 25313) },
                    { CDF3(10453, 17626, 22280) },
                    { CDF3( 8386, 14505, 19116) },
                    { CDF3( 6742, 12595, 17008) },
                    { CDF3( 4273,  8140, 11499) },
                    { CDF3(22120, 27827, 30233) },
                    { CDF3(20563, 27358, 29895) },
                    { CDF3(17076, 24644, 28153) },
                    { CDF3(13362, 20942, 25309) },
                    { CDF3(10794, 17965, 22695) },
                    { CDF3( 9014, 15652, 20319) },
                    { CDF3( 5708, 10512, 14497) },
                },
            }, {
                {
                    { CDF3( 5705, 10930, 15725) },
                    { CDF3( 7946, 12765, 16115) },
                    { CDF3( 6801, 12123, 16226) },
                    { CDF3( 5462, 10135, 14200) },
                    { CDF3( 4189,  8011, 11507) },
                    { CDF3( 3191,  6229,  9408) },
                    { CDF3( 1057,  2137,  3212) },
                    { CDF3(10018, 17067, 21491) },
                    { CDF3( 7380, 12582, 16453) },
                    { CDF3( 6068, 10845, 14339) },
                    { CDF3( 5098,  9198, 12555) },
                    { CDF3( 4312,  8010, 11119) },
                    { CDF3( 3700,  6966,  9781) },
                    { CDF3( 1693,  3326,  4887) },
                    { CDF3(18757, 24930, 27774) },
                    { CDF3(17648, 24596, 27817) },
                    { CDF3(14707, 22052, 26026) },
                    { CDF3(11720, 18852, 23292) },
                    { CDF3( 9357, 15952, 20525) },
                    { CDF3( 7810, 13753, 18210) },
                    { CDF3( 3879,  7333, 10328) },
                }, {
                    { CDF3( 8278, 13242, 15922) },
                    { CDF3(10547, 15867, 18919) },
                    { CDF3( 9106, 15842, 20609) },
                    { CDF3( 6833, 13007, 17218) },
                    { CDF3( 4811,  9712, 13923) },
                    { CDF3( 3985,  7352, 11128) },
                    { CDF3( 1688,  3458,  5262) },
                    { CDF3(12951, 21861, 26510) },
                    { CDF3( 9788, 16044, 20276) },
                    { CDF3( 6309, 11244, 14870) },
                    { CDF3( 5183,  9349, 12566) },
                    { CDF3( 4389,  8229, 11492) },
                    { CDF3( 3633,  6945, 10620) },
                    { CDF3( 3600,  6847,  9907) },
                    { CDF3(21748, 28137, 30255) },
                    { CDF3(19436, 26581, 29560) },
                    { CDF3(16359, 24201, 27953) },
                    { CDF3(13961, 21693, 25871) },
                    { CDF3(11544, 18686, 23322) },
                    { CDF3( 9372, 16462, 20952) },
                    { CDF3( 6138, 11210, 15390) },
                },
            },
        },
    }, [2] = {
        .skip = {
            {
                { CDF1(29614) }, { CDF1( 9068) }, { CDF1(12924) },
                { CDF1(19538) }, { CDF1(17737) }, { CDF1(24619) },
                { CDF1(30642) }, { CDF1( 4119) }, { CDF1(16026) },
                { CDF1(25657) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(31957) }, { CDF1( 3230) }, { CDF1(11153) },
                { CDF1(18123) }, { CDF1(20143) }, { CDF1(26536) },
                { CDF1(31986) }, { CDF1( 3050) }, { CDF1(14603) },
                { CDF1(25155) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(32363) }, { CDF1(10692) }, { CDF1(19090) },
                { CDF1(24357) }, { CDF1(24442) }, { CDF1(28312) },
                { CDF1(32169) }, { CDF1( 3648) }, { CDF1(15690) },
                { CDF1(26815) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(30669) }, { CDF1( 3832) }, { CDF1(11663) },
                { CDF1(18889) }, { CDF1(19782) }, { CDF1(23313) },
                { CDF1(31330) }, { CDF1( 5124) }, { CDF1(18719) },
                { CDF1(28468) }, { CDF1( 3082) }, { CDF1(20982) },
                { CDF1(29443) },
            }, {
                { CDF1(28573) }, { CDF1( 3183) }, { CDF1(17802) },
                { CDF1(25977) }, { CDF1(26677) }, { CDF1(27832) },
                { CDF1(32387) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            },
        }, .eob_bin_16 = {
            {
                { CDF4( 4016,  4897,  8881, 14968) },
                { CDF4(  716,  1105,  2646, 10056) },
            }, {
                { CDF4(11139, 13270, 18241, 23566) },
                { CDF4( 3192,  5032, 10297, 19755) },
            },
        }, .eob_bin_32 = {
            {
                { CDF5( 2515,  3003,  4452,  8162, 16041) },
                { CDF5(  574,   821,  1836,  5089, 13128) },
            }, {
                { CDF5(13468, 16303, 20361, 25105, 29281) },
                { CDF5( 3542,  5502, 10415, 16760, 25644) },
            },
        }, .eob_bin_64 = {
            {
                { CDF6( 2374,  2772,  4583,  7276, 12288, 19706) },
                { CDF6(  497,   810,  1315,  3000,  7004, 15641) },
            }, {
                { CDF6(15050, 17126, 21410, 24886, 28156, 30726) },
                { CDF6( 4034,  6290, 10235, 14982, 21214, 28491) },
            },
        }, .eob_bin_128 = {
            {
                { CDF7( 1366,  1738,  2527,  5016,  9355, 15797, 24643) },
                { CDF7(  354,   558,   944,  2760,  7287, 14037, 21779) },
            }, {
                { CDF7(13627, 16246, 20173, 24429, 27948, 30415, 31863) },
                { CDF7( 6275,  9889, 14769, 23164, 27988, 30493, 32272) },
            },
        }, .eob_bin_256 = {
            {
                { CDF8( 3089,  3920,  6038,  9460,
                       14266, 19881, 25766, 29176) },
                { CDF8( 1084,  2358,  3488,  5122,
                       11483, 18103, 26023, 29799) },
            }, {
                { CDF8(11514, 13794, 17480, 20754,
                       24361, 27378, 29492, 31277) },
                { CDF8( 6571,  9610, 15516, 21826,
                       29092, 30829, 31842, 32708) },
            },
        }, .eob_bin_512 = {
            { CDF9( 2624,  3936,  6480,  9686, 13979,
                   17726, 23267, 28410, 31078) },
            { CDF9(12015, 14769, 19588, 22052, 24222,
                   25812, 27300, 29219, 32114) },
        }, .eob_bin_1024 = {
            { CDF10( 2784,  3831,  7041, 10521, 14847,
                    18844, 23155, 26682, 29229, 31045) },
            { CDF10( 9577, 12466, 17739, 20750, 22061,
                    23215, 24601, 25483, 25843, 32056) },
        }, .eob_hi_bit = {
            {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(18983) },
                    { CDF1(20512) }, { CDF1(14885) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20090) },
                    { CDF1(19444) }, { CDF1(17286) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(19139) },
                    { CDF1(21487) }, { CDF1(18959) }, { CDF1(20910) },
                    { CDF1(19089) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20536) },
                    { CDF1(20664) }, { CDF1(20625) }, { CDF1(19123) },
                    { CDF1(14862) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(19833) },
                    { CDF1(21502) }, { CDF1(17485) }, { CDF1(20267) },
                    { CDF1(18353) }, { CDF1(23329) }, { CDF1(21478) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(22041) },
                    { CDF1(23434) }, { CDF1(20001) }, { CDF1(20554) },
                    { CDF1(20951) }, { CDF1(20145) }, { CDF1(15562) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(23312) },
                    { CDF1(21607) }, { CDF1(16526) }, { CDF1(18957) },
                    { CDF1(18034) }, { CDF1(18934) }, { CDF1(24247) },
                    { CDF1(16921) }, { CDF1(17080) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(26579) },
                    { CDF1(24910) }, { CDF1(18637) }, { CDF1(19800) },
                    { CDF1(20388) }, { CDF1( 9887) }, { CDF1(15642) },
                    { CDF1(30198) }, { CDF1(24721) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(26998) },
                    { CDF1(16737) }, { CDF1(17838) }, { CDF1(18922) },
                    { CDF1(19515) }, { CDF1(18636) }, { CDF1(17333) },
                    { CDF1(15776) }, { CDF1(22658) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            },
        }, .eob_base_tok = {
            {
                {
                    { CDF2(20092, 30774) }, { CDF2(30695, 32020) },
                    { CDF2(31131, 32103) }, { CDF2(28666, 30870) },
                }, {
                    { CDF2(27258, 31095) }, { CDF2(31804, 32623) },
                    { CDF2(31763, 32528) }, { CDF2(31438, 32506) },
                },
            }, {
                {
                    { CDF2(18049, 30489) }, { CDF2(31706, 32286) },
                    { CDF2(32163, 32473) }, { CDF2(31550, 32184) },
                }, {
                    { CDF2(27116, 30842) }, { CDF2(31971, 32598) },
                    { CDF2(32088, 32576) }, { CDF2(32067, 32664) },
                },
            }, {
                {
                    { CDF2(12854, 29093) }, { CDF2(32272, 32558) },
                    { CDF2(32667, 32729) }, { CDF2(32306, 32585) },
                }, {
                    { CDF2(25476, 30366) }, { CDF2(32169, 32687) },
                    { CDF2(32479, 32689) }, { CDF2(31673, 32634) },
                },
            }, {
                {
                    { CDF2( 2809, 19301) }, { CDF2(32205, 32622) },
                    { CDF2(32338, 32730) }, { CDF2(31786, 32616) },
                }, {
                    { CDF2(22737, 29105) }, { CDF2(30810, 32362) },
                    { CDF2(30014, 32627) }, { CDF2(30528, 32574) },
                },
            }, {
                {
                    { CDF2(  935,  3382) }, { CDF2(30789, 31909) },
                    { CDF2(32466, 32756) }, { CDF2(30860, 32513) },
                }, {
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                },
            },
        }, .base_tok = {
            {
                {
                    { CDF3( 8896, 16227, 20630) },
                    { CDF3(23629, 31782, 32527) },
                    { CDF3(15173, 27755, 31321) },
                    { CDF3(10158, 21233, 27382) },
                    { CDF3( 6420, 14857, 21558) },
                    { CDF3( 3269,  8155, 12646) },
                    { CDF3(24835, 32009, 32496) },
                    { CDF3(16509, 28421, 31579) },
                    { CDF3(10957, 21514, 27418) },
                    { CDF3( 7881, 15930, 22096) },
                    { CDF3( 5388, 10960, 15918) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(20745, 30773, 32093) },
                    { CDF3(15200, 27221, 30861) },
                    { CDF3(13032, 20873, 25667) },
                    { CDF3(12285, 18663, 23494) },
                    { CDF3(11563, 17481, 21489) },
                    { CDF3(26260, 31982, 32320) },
                    { CDF3(15397, 28083, 31100) },
                    { CDF3( 9742, 19217, 24824) },
                    { CDF3( 3261,  9629, 15362) },
                    { CDF3( 1480,  4322,  7499) },
                    { CDF3(27599, 32256, 32460) },
                    { CDF3(16857, 27659, 30774) },
                    { CDF3( 9551, 18290, 23748) },
                    { CDF3( 3052,  8933, 14103) },
                    { CDF3( 2021,  5910,  9787) },
                    { CDF3(29005, 32015, 32392) },
                    { CDF3(17677, 27694, 30863) },
                    { CDF3( 9204, 17356, 23219) },
                    { CDF3( 2403,  7516, 12814) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3(10808, 22056, 26896) },
                    { CDF3(25739, 32313, 32676) },
                    { CDF3(17288, 30203, 32221) },
                    { CDF3(11359, 24878, 29896) },
                    { CDF3( 6949, 17767, 24893) },
                    { CDF3( 4287, 11796, 18071) },
                    { CDF3(27880, 32521, 32705) },
                    { CDF3(19038, 31004, 32414) },
                    { CDF3(12564, 26345, 30768) },
                    { CDF3( 8269, 19947, 26779) },
                    { CDF3( 5674, 14657, 21674) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(25742, 32319, 32671) },
                    { CDF3(19557, 31164, 32454) },
                    { CDF3(13381, 26381, 30755) },
                    { CDF3(10101, 21466, 26722) },
                    { CDF3( 9209, 19650, 26825) },
                    { CDF3(27107, 31917, 32432) },
                    { CDF3(18056, 28893, 31203) },
                    { CDF3(10200, 21434, 26764) },
                    { CDF3( 4660, 12913, 19502) },
                    { CDF3( 2368,  6930, 12504) },
                    { CDF3(26960, 32158, 32613) },
                    { CDF3(18628, 30005, 32031) },
                    { CDF3(10233, 22442, 28232) },
                    { CDF3( 5471, 14630, 21516) },
                    { CDF3( 3235, 10767, 17109) },
                    { CDF3(27696, 32440, 32692) },
                    { CDF3(20032, 31167, 32438) },
                    { CDF3( 8700, 21341, 28442) },
                    { CDF3( 5662, 14831, 21795) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 9704, 17294, 21132) },
                    { CDF3(26762, 32278, 32633) },
                    { CDF3(18382, 29620, 31819) },
                    { CDF3(10891, 23475, 28723) },
                    { CDF3( 6358, 16583, 23309) },
                    { CDF3( 3248,  9118, 14141) },
                    { CDF3(27204, 32573, 32699) },
                    { CDF3(19818, 30824, 32329) },
                    { CDF3(11772, 25120, 30041) },
                    { CDF3( 6995, 18033, 25039) },
                    { CDF3( 3752, 10442, 16098) },
                    { CDF3(27222, 32256, 32559) },
                    { CDF3(15356, 28399, 31475) },
                    { CDF3( 8821, 20635, 27057) },
                    { CDF3( 5511, 14404, 21239) },
                    { CDF3( 2935,  8222, 13051) },
                    { CDF3(24875, 32120, 32529) },
                    { CDF3(15233, 28265, 31445) },
                    { CDF3( 8605, 20570, 26932) },
                    { CDF3( 5431, 14413, 21196) },
                    { CDF3( 2994,  8341, 13223) },
                    { CDF3(28201, 32604, 32700) },
                    { CDF3(21041, 31446, 32456) },
                    { CDF3(13221, 26213, 30475) },
                    { CDF3( 8255, 19385, 26037) },
                    { CDF3( 4930, 12585, 18830) },
                    { CDF3(28768, 32448, 32627) },
                    { CDF3(19705, 30561, 32021) },
                    { CDF3(11572, 23589, 28220) },
                    { CDF3( 5532, 15034, 21446) },
                    { CDF3( 2460,  7150, 11456) },
                    { CDF3(29874, 32619, 32699) },
                    { CDF3(21621, 31071, 32201) },
                    { CDF3(12511, 24747, 28992) },
                    { CDF3( 6281, 16395, 22748) },
                    { CDF3( 3246,  9278, 14497) },
                    { CDF3(29715, 32625, 32712) },
                    { CDF3(20958, 31011, 32283) },
                    { CDF3(11233, 23671, 28806) },
                    { CDF3( 6012, 16128, 22868) },
                    { CDF3( 3427,  9851, 15414) },
                }, {
                    { CDF3(11016, 22111, 26794) },
                    { CDF3(25946, 32357, 32677) },
                    { CDF3(17890, 30452, 32252) },
                    { CDF3(11678, 25142, 29816) },
                    { CDF3( 6720, 17534, 24584) },
                    { CDF3( 4230, 11665, 17820) },
                    { CDF3(28400, 32623, 32747) },
                    { CDF3(21164, 31668, 32575) },
                    { CDF3(13572, 27388, 31182) },
                    { CDF3( 8234, 20750, 27358) },
                    { CDF3( 5065, 14055, 20897) },
                    { CDF3(28981, 32547, 32705) },
                    { CDF3(18681, 30543, 32239) },
                    { CDF3(10919, 24075, 29286) },
                    { CDF3( 6431, 17199, 24077) },
                    { CDF3( 3819, 10464, 16618) },
                    { CDF3(26870, 32467, 32693) },
                    { CDF3(19041, 30831, 32347) },
                    { CDF3(11794, 25211, 30016) },
                    { CDF3( 6888, 18019, 24970) },
                    { CDF3( 4370, 12363, 18992) },
                    { CDF3(29578, 32670, 32744) },
                    { CDF3(23159, 32007, 32613) },
                    { CDF3(15315, 28669, 31676) },
                    { CDF3( 9298, 22607, 28782) },
                    { CDF3( 6144, 15913, 22968) },
                    { CDF3(28110, 32499, 32669) },
                    { CDF3(21574, 30937, 32015) },
                    { CDF3(12759, 24818, 28727) },
                    { CDF3( 6545, 16761, 23042) },
                    { CDF3( 3649, 10597, 16833) },
                    { CDF3(28163, 32552, 32728) },
                    { CDF3(22101, 31469, 32464) },
                    { CDF3(13160, 25472, 30143) },
                    { CDF3( 7303, 18684, 25468) },
                    { CDF3( 5241, 13975, 20955) },
                    { CDF3(28400, 32631, 32744) },
                    { CDF3(22104, 31793, 32603) },
                    { CDF3(13557, 26571, 30846) },
                    { CDF3( 7749, 19861, 26675) },
                    { CDF3( 4873, 14030, 21234) },
                },
            }, {
                {
                    { CDF3( 9800, 17635, 21073) },
                    { CDF3(26153, 31885, 32527) },
                    { CDF3(15038, 27852, 31006) },
                    { CDF3( 8718, 20564, 26486) },
                    { CDF3( 5128, 14076, 20514) },
                    { CDF3( 2636,  7566, 11925) },
                    { CDF3(27551, 32504, 32701) },
                    { CDF3(18310, 30054, 32100) },
                    { CDF3(10211, 23420, 29082) },
                    { CDF3( 6222, 16876, 23916) },
                    { CDF3( 3462,  9954, 15498) },
                    { CDF3(29991, 32633, 32721) },
                    { CDF3(19883, 30751, 32201) },
                    { CDF3(11141, 24184, 29285) },
                    { CDF3( 6420, 16940, 23774) },
                    { CDF3( 3392,  9753, 15118) },
                    { CDF3(28465, 32616, 32712) },
                    { CDF3(19850, 30702, 32244) },
                    { CDF3(10983, 24024, 29223) },
                    { CDF3( 6294, 16770, 23582) },
                    { CDF3( 3244,  9283, 14509) },
                    { CDF3(30023, 32717, 32748) },
                    { CDF3(22940, 32032, 32626) },
                    { CDF3(14282, 27928, 31473) },
                    { CDF3( 8562, 21327, 27914) },
                    { CDF3( 4846, 13393, 19919) },
                    { CDF3(29981, 32590, 32695) },
                    { CDF3(20465, 30963, 32166) },
                    { CDF3(11479, 23579, 28195) },
                    { CDF3( 5916, 15648, 22073) },
                    { CDF3( 3031,  8605, 13398) },
                    { CDF3(31146, 32691, 32739) },
                    { CDF3(23106, 31724, 32444) },
                    { CDF3(13783, 26738, 30439) },
                    { CDF3( 7852, 19468, 25807) },
                    { CDF3( 3860, 11124, 16853) },
                    { CDF3(31014, 32724, 32748) },
                    { CDF3(23629, 32109, 32628) },
                    { CDF3(14747, 28115, 31403) },
                    { CDF3( 8545, 21242, 27478) },
                    { CDF3( 4574, 12781, 19067) },
                }, {
                    { CDF3( 9185, 19694, 24688) },
                    { CDF3(26081, 31985, 32621) },
                    { CDF3(16015, 29000, 31787) },
                    { CDF3(10542, 23690, 29206) },
                    { CDF3( 6732, 17945, 24677) },
                    { CDF3( 3916, 11039, 16722) },
                    { CDF3(28224, 32566, 32744) },
                    { CDF3(19100, 31138, 32485) },
                    { CDF3(12528, 26620, 30879) },
                    { CDF3( 7741, 20277, 26885) },
                    { CDF3( 4566, 12845, 18990) },
                    { CDF3(29933, 32593, 32718) },
                    { CDF3(17670, 30333, 32155) },
                    { CDF3(10385, 23600, 28909) },
                    { CDF3( 6243, 16236, 22407) },
                    { CDF3( 3976, 10389, 16017) },
                    { CDF3(28377, 32561, 32738) },
                    { CDF3(19366, 31175, 32482) },
                    { CDF3(13327, 27175, 31094) },
                    { CDF3( 8258, 20769, 27143) },
                    { CDF3( 4703, 13198, 19527) },
                    { CDF3(31086, 32706, 32748) },
                    { CDF3(22853, 31902, 32583) },
                    { CDF3(14759, 28186, 31419) },
                    { CDF3( 9284, 22382, 28348) },
                    { CDF3( 5585, 15192, 21868) },
                    { CDF3(28291, 32652, 32746) },
                    { CDF3(19849, 32107, 32571) },
                    { CDF3(14834, 26818, 29214) },
                    { CDF3(10306, 22594, 28672) },
                    { CDF3( 6615, 17384, 23384) },
                    { CDF3(28947, 32604, 32745) },
                    { CDF3(25625, 32289, 32646) },
                    { CDF3(18758, 28672, 31403) },
                    { CDF3(10017, 23430, 28523) },
                    { CDF3( 6862, 15269, 22131) },
                    { CDF3(23933, 32509, 32739) },
                    { CDF3(19927, 31495, 32631) },
                    { CDF3(11903, 26023, 30621) },
                    { CDF3( 7026, 20094, 27252) },
                    { CDF3( 5998, 18106, 24437) },
                },
            }, {
                {
                    { CDF3( 4456, 11274, 15533) },
                    { CDF3(21219, 29079, 31616) },
                    { CDF3(11173, 23774, 28567) },
                    { CDF3( 7282, 18293, 24263) },
                    { CDF3( 4890, 13286, 19115) },
                    { CDF3( 1890,  5508,  8659) },
                    { CDF3(26651, 32136, 32647) },
                    { CDF3(14630, 28254, 31455) },
                    { CDF3( 8716, 21287, 27395) },
                    { CDF3( 5615, 15331, 22008) },
                    { CDF3( 2675,  7700, 12150) },
                    { CDF3(29954, 32526, 32690) },
                    { CDF3(16126, 28982, 31633) },
                    { CDF3( 9030, 21361, 27352) },
                    { CDF3( 5411, 14793, 21271) },
                    { CDF3( 2943,  8422, 13163) },
                    { CDF3(29539, 32601, 32730) },
                    { CDF3(18125, 30385, 32201) },
                    { CDF3(10422, 24090, 29468) },
                    { CDF3( 6468, 17487, 24438) },
                    { CDF3( 2970,  8653, 13531) },
                    { CDF3(30912, 32715, 32748) },
                    { CDF3(20666, 31373, 32497) },
                    { CDF3(12509, 26640, 30917) },
                    { CDF3( 8058, 20629, 27290) },
                    { CDF3( 4231, 12006, 18052) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3(10202, 20633, 25484) },
                    { CDF3(27336, 31445, 32352) },
                    { CDF3(12420, 24384, 28552) },
                    { CDF3( 7648, 18115, 23856) },
                    { CDF3( 5662, 14341, 19902) },
                    { CDF3( 3611, 10328, 15390) },
                    { CDF3(30945, 32616, 32736) },
                    { CDF3(18682, 30505, 32253) },
                    { CDF3(11513, 25336, 30203) },
                    { CDF3( 7449, 19452, 26148) },
                    { CDF3( 4482, 13051, 18886) },
                    { CDF3(32022, 32690, 32747) },
                    { CDF3(18578, 30501, 32146) },
                    { CDF3(11249, 23368, 28631) },
                    { CDF3( 5645, 16958, 22158) },
                    { CDF3( 5009, 11444, 16637) },
                    { CDF3(31357, 32710, 32748) },
                    { CDF3(21552, 31494, 32504) },
                    { CDF3(13891, 27677, 31340) },
                    { CDF3( 9051, 22098, 28172) },
                    { CDF3( 5190, 13377, 19486) },
                    { CDF3(32364, 32740, 32748) },
                    { CDF3(24839, 31907, 32551) },
                    { CDF3(17160, 28779, 31696) },
                    { CDF3(12452, 24137, 29602) },
                    { CDF3( 6165, 15389, 22477) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 2575,  7281, 11077) },
                    { CDF3(14002, 20866, 25402) },
                    { CDF3( 6343, 15056, 19658) },
                    { CDF3( 4474, 11858, 17041) },
                    { CDF3( 2865,  8299, 12534) },
                    { CDF3( 1344,  3949,  6391) },
                    { CDF3(24720, 31239, 32459) },
                    { CDF3(12585, 25356, 29968) },
                    { CDF3( 7181, 18246, 24444) },
                    { CDF3( 5025, 13667, 19885) },
                    { CDF3( 2521,  7304, 11605) },
                    { CDF3(29908, 32252, 32584) },
                    { CDF3(17421, 29156, 31575) },
                    { CDF3( 9889, 22188, 27782) },
                    { CDF3( 5878, 15647, 22123) },
                    { CDF3( 2814,  8665, 13323) },
                    { CDF3(30183, 32568, 32713) },
                    { CDF3(18528, 30195, 32049) },
                    { CDF3(10982, 24606, 29657) },
                    { CDF3( 6957, 18165, 25231) },
                    { CDF3( 3508, 10118, 15468) },
                    { CDF3(31761, 32736, 32748) },
                    { CDF3(21041, 31328, 32546) },
                    { CDF3(12568, 26732, 31166) },
                    { CDF3( 8052, 20720, 27733) },
                    { CDF3( 4336, 12192, 18396) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            },
        }, .dc_sign = {
            { { CDF1(16000) }, { CDF1(13056) }, { CDF1(18816) } },
            { { CDF1(15232) }, { CDF1(12928) }, { CDF1(17280) } },
        }, .br_tok = {
            {
                {
                    { CDF3(16138, 22223, 25509) },
                    { CDF3(15347, 22430, 26332) },
                    { CDF3( 9614, 16736, 21332) },
                    { CDF3( 6600, 12275, 16907) },
                    { CDF3( 4811,  9424, 13547) },
                    { CDF3( 3748,  7809, 11420) },
                    { CDF3( 2254,  4587,  6890) },
                    { CDF3(15196, 20284, 23177) },
                    { CDF3(18317, 25469, 28451) },
                    { CDF3(13918, 21651, 25842) },
                    { CDF3(10052, 17150, 21995) },
                    { CDF3( 7499, 13630, 18587) },
                    { CDF3( 6158, 11417, 16003) },
                    { CDF3( 4014,  7785, 11252) },
                    { CDF3(15048, 21067, 24384) },
                    { CDF3(18202, 25346, 28553) },
                    { CDF3(14302, 22019, 26356) },
                    { CDF3(10839, 18139, 23166) },
                    { CDF3( 8715, 15744, 20806) },
                    { CDF3( 7536, 13576, 18544) },
                    { CDF3( 5413, 10335, 14498) },
                }, {
                    { CDF3(17394, 24501, 27895) },
                    { CDF3(15889, 23420, 27185) },
                    { CDF3(11561, 19133, 23870) },
                    { CDF3( 8285, 14812, 19844) },
                    { CDF3( 6496, 12043, 16550) },
                    { CDF3( 4771,  9574, 13677) },
                    { CDF3( 3603,  6830, 10144) },
                    { CDF3(21656, 27704, 30200) },
                    { CDF3(21324, 27915, 30511) },
                    { CDF3(17327, 25336, 28997) },
                    { CDF3(13417, 21381, 26033) },
                    { CDF3(10132, 17425, 22338) },
                    { CDF3( 8580, 15016, 19633) },
                    { CDF3( 5694, 11477, 16411) },
                    { CDF3(24116, 29780, 31450) },
                    { CDF3(23853, 29695, 31591) },
                    { CDF3(20085, 27614, 30428) },
                    { CDF3(15326, 24335, 28575) },
                    { CDF3(11814, 19472, 24810) },
                    { CDF3(10221, 18611, 24767) },
                    { CDF3( 7689, 14558, 20321) },
                },
            }, {
                {
                    { CDF3(16214, 22380, 25770) },
                    { CDF3(14213, 21304, 25295) },
                    { CDF3( 9213, 15823, 20455) },
                    { CDF3( 6395, 11758, 16139) },
                    { CDF3( 4779,  9187, 13066) },
                    { CDF3( 3821,  7501, 10953) },
                    { CDF3( 2293,  4567,  6795) },
                    { CDF3(15859, 21283, 23820) },
                    { CDF3(18404, 25602, 28726) },
                    { CDF3(14325, 21980, 26206) },
                    { CDF3(10669, 17937, 22720) },
                    { CDF3( 8297, 14642, 19447) },
                    { CDF3( 6746, 12389, 16893) },
                    { CDF3( 4324,  8251, 11770) },
                    { CDF3(16532, 21631, 24475) },
                    { CDF3(20667, 27150, 29668) },
                    { CDF3(16728, 24510, 28175) },
                    { CDF3(12861, 20645, 25332) },
                    { CDF3(10076, 17361, 22417) },
                    { CDF3( 8395, 14940, 19963) },
                    { CDF3( 5731, 10683, 14912) },
                }, {
                    { CDF3(14433, 21155, 24938) },
                    { CDF3(14658, 21716, 25545) },
                    { CDF3( 9923, 16824, 21557) },
                    { CDF3( 6982, 13052, 17721) },
                    { CDF3( 5419, 10503, 15050) },
                    { CDF3( 4852,  9162, 13014) },
                    { CDF3( 3271,  6395,  9630) },
                    { CDF3(22210, 27833, 30109) },
                    { CDF3(20750, 27368, 29821) },
                    { CDF3(16894, 24828, 28573) },
                    { CDF3(13247, 21276, 25757) },
                    { CDF3(10038, 17265, 22563) },
                    { CDF3( 8587, 14947, 20327) },
                    { CDF3( 5645, 11371, 15252) },
                    { CDF3(22027, 27526, 29714) },
                    { CDF3(23098, 29146, 31221) },
                    { CDF3(19886, 27341, 30272) },
                    { CDF3(15609, 23747, 28046) },
                    { CDF3(11993, 20065, 24939) },
                    { CDF3( 9637, 18267, 23671) },
                    { CDF3( 7625, 13801, 19144) },
                },
            }, {
                {
                    { CDF3(14438, 20798, 24089) },
                    { CDF3(12621, 19203, 23097) },
                    { CDF3( 8177, 14125, 18402) },
                    { CDF3( 5674, 10501, 14456) },
                    { CDF3( 4236,  8239, 11733) },
                    { CDF3( 3447,  6750,  9806) },
                    { CDF3( 1986,  3950,  5864) },
                    { CDF3(16208, 22099, 24930) },
                    { CDF3(16537, 24025, 27585) },
                    { CDF3(12780, 20381, 24867) },
                    { CDF3( 9767, 16612, 21416) },
                    { CDF3( 7686, 13738, 18398) },
                    { CDF3( 6333, 11614, 15964) },
                    { CDF3( 3941,  7571, 10836) },
                    { CDF3(22819, 27422, 29202) },
                    { CDF3(22224, 28514, 30721) },
                    { CDF3(17660, 25433, 28913) },
                    { CDF3(13574, 21482, 26002) },
                    { CDF3(10629, 17977, 22938) },
                    { CDF3( 8612, 15298, 20265) },
                    { CDF3( 5607, 10491, 14596) },
                }, {
                    { CDF3(13569, 19800, 23206) },
                    { CDF3(13128, 19924, 23869) },
                    { CDF3( 8329, 14841, 19403) },
                    { CDF3( 6130, 10976, 15057) },
                    { CDF3( 4682,  8839, 12518) },
                    { CDF3( 3656,  7409, 10588) },
                    { CDF3( 2577,  5099,  7412) },
                    { CDF3(22427, 28684, 30585) },
                    { CDF3(20913, 27750, 30139) },
                    { CDF3(15840, 24109, 27834) },
                    { CDF3(12308, 20029, 24569) },
                    { CDF3(10216, 16785, 21458) },
                    { CDF3( 8309, 14203, 19113) },
                    { CDF3( 6043, 11168, 15307) },
                    { CDF3(23166, 28901, 30998) },
                    { CDF3(21899, 28405, 30751) },
                    { CDF3(18413, 26091, 29443) },
                    { CDF3(15233, 23114, 27352) },
                    { CDF3(12683, 20472, 25288) },
                    { CDF3(10702, 18259, 23409) },
                    { CDF3( 8125, 14464, 19226) },
                },
            }, {
                {
                    { CDF3( 9040, 14786, 18360) },
                    { CDF3( 9979, 15718, 19415) },
                    { CDF3( 7913, 13918, 18311) },
                    { CDF3( 5859, 10889, 15184) },
                    { CDF3( 4593,  8677, 12510) },
                    { CDF3( 3820,  7396, 10791) },
                    { CDF3( 1730,  3471,  5192) },
                    { CDF3(11803, 18365, 22709) },
                    { CDF3(11419, 18058, 22225) },
                    { CDF3( 9418, 15774, 20243) },
                    { CDF3( 7539, 13325, 17657) },
                    { CDF3( 6233, 11317, 15384) },
                    { CDF3( 5137,  9656, 13545) },
                    { CDF3( 2977,  5774,  8349) },
                    { CDF3(21207, 27246, 29640) },
                    { CDF3(19547, 26578, 29497) },
                    { CDF3(16169, 23871, 27690) },
                    { CDF3(12820, 20458, 25018) },
                    { CDF3(10224, 17332, 22214) },
                    { CDF3( 8526, 15048, 19884) },
                    { CDF3( 5037,  9410, 13118) },
                }, {
                    { CDF3(12339, 17329, 20140) },
                    { CDF3(13505, 19895, 23225) },
                    { CDF3( 9847, 16944, 21564) },
                    { CDF3( 7280, 13256, 18348) },
                    { CDF3( 4712, 10009, 14454) },
                    { CDF3( 4361,  7914, 12477) },
                    { CDF3( 2870,  5628,  7995) },
                    { CDF3(20061, 25504, 28526) },
                    { CDF3(15235, 22878, 26145) },
                    { CDF3(12985, 19958, 24155) },
                    { CDF3( 9782, 16641, 21403) },
                    { CDF3( 9456, 16360, 20760) },
                    { CDF3( 6855, 12940, 18557) },
                    { CDF3( 5661, 10564, 15002) },
                    { CDF3(25656, 30602, 31894) },
                    { CDF3(22570, 29107, 31092) },
                    { CDF3(18917, 26423, 29541) },
                    { CDF3(15940, 23649, 27754) },
                    { CDF3(12803, 20581, 25219) },
                    { CDF3(11082, 18695, 23376) },
                    { CDF3( 7939, 14373, 19005) },
                },
            },
        },
    }, [3] = {
        .skip = {
            {
                { CDF1(26887) }, { CDF1( 6729) }, { CDF1(10361) },
                { CDF1(17442) }, { CDF1(15045) }, { CDF1(22478) },
                { CDF1(29072) }, { CDF1( 2713) }, { CDF1(11861) },
                { CDF1(20773) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(31903) }, { CDF1( 2044) }, { CDF1( 7528) },
                { CDF1(14618) }, { CDF1(16182) }, { CDF1(24168) },
                { CDF1(31037) }, { CDF1( 2786) }, { CDF1(11194) },
                { CDF1(20155) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(32510) }, { CDF1( 8430) }, { CDF1(17318) },
                { CDF1(24154) }, { CDF1(23674) }, { CDF1(28789) },
                { CDF1(32139) }, { CDF1( 3440) }, { CDF1(13117) },
                { CDF1(22702) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            }, {
                { CDF1(31671) }, { CDF1( 2056) }, { CDF1(11746) },
                { CDF1(16852) }, { CDF1(18635) }, { CDF1(24715) },
                { CDF1(31484) }, { CDF1( 4656) }, { CDF1(16074) },
                { CDF1(24704) }, { CDF1( 1806) }, { CDF1(14645) },
                { CDF1(25336) },
            }, {
                { CDF1(31539) }, { CDF1( 8433) }, { CDF1(20576) },
                { CDF1(27904) }, { CDF1(27852) }, { CDF1(30026) },
                { CDF1(32441) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                { CDF1(16384) },
            },
        }, .eob_bin_16 = {
            {
                { CDF4( 6708,  8958, 14746, 22133) },
                { CDF4( 1222,  2074,  4783, 15410) },
            }, {
                { CDF4(19575, 21766, 26044, 29709) },
                { CDF4( 7297, 10767, 19273, 28194) },
            },
        }, .eob_bin_32 = {
            {
                { CDF5( 4617,  5709,  8446, 13584, 23135) },
                { CDF5( 1156,  1702,  3675,  9274, 20539) },
            }, {
                { CDF5(22086, 24282, 27010, 29770, 31743) },
                { CDF5( 7699, 10897, 20891, 26926, 31628) },
            },
        }, .eob_bin_64 = {
            {
                { CDF6( 6307,  7541, 12060, 16358, 22553, 27865) },
                { CDF6( 1289,  2320,  3971,  7926, 14153, 24291) },
            }, {
                { CDF6(24212, 25708, 28268, 30035, 31307, 32049) },
                { CDF6( 8726, 12378, 19409, 26450, 30038, 32462) },
            },
        }, .eob_bin_128 = {
            {
                { CDF7( 3472,  4885,  7489, 12481, 18517, 24536, 29635) },
                { CDF7(  886,  1731,  3271,  8469, 15569, 22126, 28383) },
            }, {
                { CDF7(24313, 26062, 28385, 30107, 31217, 31898, 32345) },
                { CDF7( 9165, 13282, 21150, 30286, 31894, 32571, 32712) },
            },
        }, .eob_bin_256 = {
            {
                { CDF8( 5348,  7113, 11820, 15924,
                       22106, 26777, 30334, 31757) },
                { CDF8( 2453,  4474,  6307,  8777,
                       16474, 22975, 29000, 31547) },
            }, {
                { CDF8(23110, 24597, 27140, 28894,
                       30167, 30927, 31392, 32094) },
                { CDF8( 9998, 17661, 25178, 28097,
                       31308, 32038, 32403, 32695) },
            },
        }, .eob_bin_512 = {
            { CDF9( 5927,  7809, 10923, 14597, 19439,
                   24135, 28456, 31142, 32060) },
            { CDF9(21093, 23043, 25742, 27658, 29097,
                   29716, 30073, 30820, 31956) },
        }, .eob_bin_1024 = {
            { CDF10( 6698,  8334, 11961, 15762, 20186,
                    23862, 27434, 29326, 31082, 32050) },
            { CDF10(20569, 22426, 25569, 26859, 28053,
                    28913, 29486, 29724, 29807, 32570) },
        }, .eob_hi_bit = {
            {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20177) },
                    { CDF1(20789) }, { CDF1(20262) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(21416) },
                    { CDF1(20855) }, { CDF1(23410) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20238) },
                    { CDF1(21057) }, { CDF1(19159) }, { CDF1(22337) },
                    { CDF1(20159) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(20125) },
                    { CDF1(20559) }, { CDF1(21707) }, { CDF1(22296) },
                    { CDF1(17333) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(19941) },
                    { CDF1(20527) }, { CDF1(21470) }, { CDF1(22487) },
                    { CDF1(19558) }, { CDF1(22354) }, { CDF1(20331) },
                    { CDF1(16384) }, { CDF1(16384) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(22752) },
                    { CDF1(25006) }, { CDF1(22075) }, { CDF1(21576) },
                    { CDF1(17740) }, { CDF1(21690) }, { CDF1(19211) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(21442) },
                    { CDF1(22358) }, { CDF1(18503) }, { CDF1(20291) },
                    { CDF1(19945) }, { CDF1(21294) }, { CDF1(21178) },
                    { CDF1(19400) }, { CDF1(10556) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(24648) },
                    { CDF1(24949) }, { CDF1(20708) }, { CDF1(23905) },
                    { CDF1(20501) }, { CDF1( 9558) }, { CDF1( 9423) },
                    { CDF1(30365) }, { CDF1(19253) },
                },
            }, {
                {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(26064) },
                    { CDF1(22098) }, { CDF1(19613) }, { CDF1(20525) },
                    { CDF1(17595) }, { CDF1(16618) }, { CDF1(20497) },
                    { CDF1(18989) }, { CDF1(15513) },
                }, {
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) }, { CDF1(16384) },
                    { CDF1(16384) }, { CDF1(16384) },
                },
            },
        }, .eob_base_tok = {
            {
                {
                    { CDF2(22497, 31198) }, { CDF2(31715, 32495) },
                    { CDF2(31606, 32337) }, { CDF2(30388, 31990) },
                }, {
                    { CDF2(27877, 31584) }, { CDF2(32170, 32728) },
                    { CDF2(32155, 32688) }, { CDF2(32219, 32702) },
                },
            }, {
                {
                    { CDF2(21457, 31043) }, { CDF2(31951, 32483) },
                    { CDF2(32153, 32562) }, { CDF2(31473, 32215) },
                }, {
                    { CDF2(27558, 31151) }, { CDF2(32020, 32640) },
                    { CDF2(32097, 32575) }, { CDF2(32242, 32719) },
                },
            }, {
                {
                    { CDF2(19980, 30591) }, { CDF2(32219, 32597) },
                    { CDF2(32581, 32706) }, { CDF2(31803, 32287) },
                }, {
                    { CDF2(26473, 30507) }, { CDF2(32431, 32723) },
                    { CDF2(32196, 32611) }, { CDF2(31588, 32528) },
                },
            }, {
                {
                    { CDF2(24647, 30463) }, { CDF2(32412, 32695) },
                    { CDF2(32468, 32720) }, { CDF2(31269, 32523) },
                }, {
                    { CDF2(28482, 31505) }, { CDF2(32152, 32701) },
                    { CDF2(31732, 32598) }, { CDF2(31767, 32712) },
                },
            }, {
                {
                    { CDF2(12358, 24977) }, { CDF2(31331, 32385) },
                    { CDF2(32634, 32756) }, { CDF2(30411, 32548) },
                }, {
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                    { CDF2(10923, 21845) }, { CDF2(10923, 21845) },
                },
            },
        }, .base_tok = {
            {
                {
                    { CDF3( 7062, 16472, 22319) },
                    { CDF3(24538, 32261, 32674) },
                    { CDF3(13675, 28041, 31779) },
                    { CDF3( 8590, 20674, 27631) },
                    { CDF3( 5685, 14675, 22013) },
                    { CDF3( 3655,  9898, 15731) },
                    { CDF3(26493, 32418, 32658) },
                    { CDF3(16376, 29342, 32090) },
                    { CDF3(10594, 22649, 28970) },
                    { CDF3( 8176, 17170, 24303) },
                    { CDF3( 5605, 12694, 19139) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(23888, 31902, 32542) },
                    { CDF3(18612, 29687, 31987) },
                    { CDF3(16245, 24852, 29249) },
                    { CDF3(15765, 22608, 27559) },
                    { CDF3(19895, 24699, 27510) },
                    { CDF3(28401, 32212, 32457) },
                    { CDF3(15274, 27825, 30980) },
                    { CDF3( 9364, 18128, 24332) },
                    { CDF3( 2283,  8193, 15082) },
                    { CDF3( 1228,  3972,  7881) },
                    { CDF3(29455, 32469, 32620) },
                    { CDF3(17981, 28245, 31388) },
                    { CDF3(10921, 20098, 26240) },
                    { CDF3( 3743, 11829, 18657) },
                    { CDF3( 2374,  9593, 15715) },
                    { CDF3(31068, 32466, 32635) },
                    { CDF3(20321, 29572, 31971) },
                    { CDF3(10771, 20255, 27119) },
                    { CDF3( 2795, 10410, 17361) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 9320, 22102, 27840) },
                    { CDF3(27057, 32464, 32724) },
                    { CDF3(16331, 30268, 32309) },
                    { CDF3(10319, 23935, 29720) },
                    { CDF3( 6189, 16448, 24106) },
                    { CDF3( 3589, 10884, 18808) },
                    { CDF3(29026, 32624, 32748) },
                    { CDF3(19226, 31507, 32587) },
                    { CDF3(12692, 26921, 31203) },
                    { CDF3( 7049, 19532, 27635) },
                    { CDF3( 7727, 15669, 23252) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3(28056, 32625, 32748) },
                    { CDF3(22383, 32075, 32669) },
                    { CDF3(15417, 27098, 31749) },
                    { CDF3(18127, 26493, 27190) },
                    { CDF3( 5461, 16384, 21845) },
                    { CDF3(27982, 32091, 32584) },
                    { CDF3(19045, 29868, 31972) },
                    { CDF3(10397, 22266, 27932) },
                    { CDF3( 5990, 13697, 21500) },
                    { CDF3( 1792,  6912, 15104) },
                    { CDF3(28198, 32501, 32718) },
                    { CDF3(21534, 31521, 32569) },
                    { CDF3(11109, 25217, 30017) },
                    { CDF3( 5671, 15124, 26151) },
                    { CDF3( 4681, 14043, 18725) },
                    { CDF3(28688, 32580, 32741) },
                    { CDF3(22576, 32079, 32661) },
                    { CDF3(10627, 22141, 28340) },
                    { CDF3( 9362, 14043, 28087) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 7754, 16948, 22142) },
                    { CDF3(25670, 32330, 32691) },
                    { CDF3(15663, 29225, 31994) },
                    { CDF3( 9878, 23288, 29158) },
                    { CDF3( 6419, 17088, 24336) },
                    { CDF3( 3859, 11003, 17039) },
                    { CDF3(27562, 32595, 32725) },
                    { CDF3(17575, 30588, 32399) },
                    { CDF3(10819, 24838, 30309) },
                    { CDF3( 7124, 18686, 25916) },
                    { CDF3( 4479, 12688, 19340) },
                    { CDF3(28385, 32476, 32673) },
                    { CDF3(15306, 29005, 31938) },
                    { CDF3( 8937, 21615, 28322) },
                    { CDF3( 5982, 15603, 22786) },
                    { CDF3( 3620, 10267, 16136) },
                    { CDF3(27280, 32464, 32667) },
                    { CDF3(15607, 29160, 32004) },
                    { CDF3( 9091, 22135, 28740) },
                    { CDF3( 6232, 16632, 24020) },
                    { CDF3( 4047, 11377, 17672) },
                    { CDF3(29220, 32630, 32718) },
                    { CDF3(19650, 31220, 32462) },
                    { CDF3(13050, 26312, 30827) },
                    { CDF3( 9228, 20870, 27468) },
                    { CDF3( 6146, 15149, 21971) },
                    { CDF3(30169, 32481, 32623) },
                    { CDF3(17212, 29311, 31554) },
                    { CDF3( 9911, 21311, 26882) },
                    { CDF3( 4487, 13314, 20372) },
                    { CDF3( 2570,  7772, 12889) },
                    { CDF3(30924, 32613, 32708) },
                    { CDF3(19490, 30206, 32107) },
                    { CDF3(11232, 23998, 29276) },
                    { CDF3( 6769, 17955, 25035) },
                    { CDF3( 4398, 12623, 19214) },
                    { CDF3(30609, 32627, 32722) },
                    { CDF3(19370, 30582, 32287) },
                    { CDF3(10457, 23619, 29409) },
                    { CDF3( 6443, 17637, 24834) },
                    { CDF3( 4645, 13236, 20106) },
                }, {
                    { CDF3( 8626, 20271, 26216) },
                    { CDF3(26707, 32406, 32711) },
                    { CDF3(16999, 30329, 32286) },
                    { CDF3(11445, 25123, 30286) },
                    { CDF3( 6411, 18828, 25601) },
                    { CDF3( 6801, 12458, 20248) },
                    { CDF3(29918, 32682, 32748) },
                    { CDF3(20649, 31739, 32618) },
                    { CDF3(12879, 27773, 31581) },
                    { CDF3( 7896, 21751, 28244) },
                    { CDF3( 5260, 14870, 23698) },
                    { CDF3(29252, 32593, 32731) },
                    { CDF3(17072, 30460, 32294) },
                    { CDF3(10653, 24143, 29365) },
                    { CDF3( 6536, 17490, 23983) },
                    { CDF3( 4929, 13170, 20085) },
                    { CDF3(28137, 32518, 32715) },
                    { CDF3(18171, 30784, 32407) },
                    { CDF3(11437, 25436, 30459) },
                    { CDF3( 7252, 18534, 26176) },
                    { CDF3( 4126, 13353, 20978) },
                    { CDF3(31162, 32726, 32748) },
                    { CDF3(23017, 32222, 32701) },
                    { CDF3(15629, 29233, 32046) },
                    { CDF3( 9387, 22621, 29480) },
                    { CDF3( 6922, 17616, 25010) },
                    { CDF3(28838, 32265, 32614) },
                    { CDF3(19701, 30206, 31920) },
                    { CDF3(11214, 22410, 27933) },
                    { CDF3( 5320, 14177, 23034) },
                    { CDF3( 5049, 12881, 17827) },
                    { CDF3(27484, 32471, 32734) },
                    { CDF3(21076, 31526, 32561) },
                    { CDF3(12707, 26303, 31211) },
                    { CDF3( 8169, 21722, 28219) },
                    { CDF3( 6045, 19406, 27042) },
                    { CDF3(27753, 32572, 32745) },
                    { CDF3(20832, 31878, 32653) },
                    { CDF3(13250, 27356, 31674) },
                    { CDF3( 7718, 21508, 29858) },
                    { CDF3( 7209, 18350, 25559) },
                },
            }, {
                {
                    { CDF3( 7876, 16901, 21741) },
                    { CDF3(24001, 31898, 32625) },
                    { CDF3(14529, 27959, 31451) },
                    { CDF3( 8273, 20818, 27258) },
                    { CDF3( 5278, 14673, 21510) },
                    { CDF3( 2983,  8843, 14039) },
                    { CDF3(28016, 32574, 32732) },
                    { CDF3(17471, 30306, 32301) },
                    { CDF3(10224, 24063, 29728) },
                    { CDF3( 6602, 17954, 25052) },
                    { CDF3( 4002, 11585, 17759) },
                    { CDF3(30190, 32634, 32739) },
                    { CDF3(17497, 30282, 32270) },
                    { CDF3(10229, 23729, 29538) },
                    { CDF3( 6344, 17211, 24440) },
                    { CDF3( 3849, 11189, 17108) },
                    { CDF3(28570, 32583, 32726) },
                    { CDF3(17521, 30161, 32238) },
                    { CDF3(10153, 23565, 29378) },
                    { CDF3( 6455, 17341, 24443) },
                    { CDF3( 3907, 11042, 17024) },
                    { CDF3(30689, 32715, 32748) },
                    { CDF3(21546, 31840, 32610) },
                    { CDF3(13547, 27581, 31459) },
                    { CDF3( 8912, 21757, 28309) },
                    { CDF3( 5548, 15080, 22046) },
                    { CDF3(30783, 32540, 32685) },
                    { CDF3(17540, 29528, 31668) },
                    { CDF3(10160, 21468, 26783) },
                    { CDF3( 4724, 13393, 20054) },
                    { CDF3( 2702,  8174, 13102) },
                    { CDF3(31648, 32686, 32742) },
                    { CDF3(20954, 31094, 32337) },
                    { CDF3(12420, 25698, 30179) },
                    { CDF3( 7304, 19320, 26248) },
                    { CDF3( 4366, 12261, 18864) },
                    { CDF3(31581, 32723, 32748) },
                    { CDF3(21373, 31586, 32525) },
                    { CDF3(12744, 26625, 30885) },
                    { CDF3( 7431, 20322, 26950) },
                    { CDF3( 4692, 13323, 20111) },
                }, {
                    { CDF3( 7833, 18369, 24095) },
                    { CDF3(26650, 32273, 32702) },
                    { CDF3(16371, 29961, 32191) },
                    { CDF3(11055, 24082, 29629) },
                    { CDF3( 6892, 18644, 25400) },
                    { CDF3( 5006, 13057, 19240) },
                    { CDF3(29834, 32666, 32748) },
                    { CDF3(19577, 31335, 32570) },
                    { CDF3(12253, 26509, 31122) },
                    { CDF3( 7991, 20772, 27711) },
                    { CDF3( 5677, 15910, 23059) },
                    { CDF3(30109, 32532, 32720) },
                    { CDF3(16747, 30166, 32252) },
                    { CDF3(10134, 23542, 29184) },
                    { CDF3( 5791, 16176, 23556) },
                    { CDF3( 4362, 10414, 17284) },
                    { CDF3(29492, 32626, 32748) },
                    { CDF3(19894, 31402, 32525) },
                    { CDF3(12942, 27071, 30869) },
                    { CDF3( 8346, 21216, 27405) },
                    { CDF3( 6572, 17087, 23859) },
                    { CDF3(32035, 32735, 32748) },
                    { CDF3(22957, 31838, 32618) },
                    { CDF3(14724, 28572, 31772) },
                    { CDF3(10364, 23999, 29553) },
                    { CDF3( 7004, 18433, 25655) },
                    { CDF3(27528, 32277, 32681) },
                    { CDF3(16959, 31171, 32096) },
                    { CDF3(10486, 23593, 27962) },
                    { CDF3( 8192, 16384, 23211) },
                    { CDF3( 8937, 17873, 20852) },
                    { CDF3(27715, 32002, 32615) },
                    { CDF3(15073, 29491, 31676) },
                    { CDF3(11264, 24576, 28672) },
                    { CDF3( 2341, 18725, 23406) },
                    { CDF3( 7282, 18204, 25486) },
                    { CDF3(28547, 32213, 32657) },
                    { CDF3(20788, 29773, 32239) },
                    { CDF3( 6780, 21469, 30508) },
                    { CDF3( 5958, 14895, 23831) },
                    { CDF3(16384, 21845, 27307) },
                },
            }, {
                {
                    { CDF3( 5992, 14304, 19765) },
                    { CDF3(22612, 31238, 32456) },
                    { CDF3(13456, 27162, 31087) },
                    { CDF3( 8001, 20062, 26504) },
                    { CDF3( 5168, 14105, 20764) },
                    { CDF3( 2632,  7771, 12385) },
                    { CDF3(27034, 32344, 32709) },
                    { CDF3(15850, 29415, 31997) },
                    { CDF3( 9494, 22776, 28841) },
                    { CDF3( 6151, 16830, 23969) },
                    { CDF3( 3461, 10039, 15722) },
                    { CDF3(30134, 32569, 32731) },
                    { CDF3(15638, 29422, 31945) },
                    { CDF3( 9150, 21865, 28218) },
                    { CDF3( 5647, 15719, 22676) },
                    { CDF3( 3402,  9772, 15477) },
                    { CDF3(28530, 32586, 32735) },
                    { CDF3(17139, 30298, 32292) },
                    { CDF3(10200, 24039, 29685) },
                    { CDF3( 6419, 17674, 24786) },
                    { CDF3( 3544, 10225, 15824) },
                    { CDF3(31333, 32726, 32748) },
                    { CDF3(20618, 31487, 32544) },
                    { CDF3(12901, 27217, 31232) },
                    { CDF3( 8624, 21734, 28171) },
                    { CDF3( 5104, 14191, 20748) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3(11206, 21090, 26561) },
                    { CDF3(28759, 32279, 32671) },
                    { CDF3(14171, 27952, 31569) },
                    { CDF3( 9743, 22907, 29141) },
                    { CDF3( 6871, 17886, 24868) },
                    { CDF3( 4960, 13152, 19315) },
                    { CDF3(31077, 32661, 32748) },
                    { CDF3(19400, 31195, 32515) },
                    { CDF3(12752, 26858, 31040) },
                    { CDF3( 8370, 22098, 28591) },
                    { CDF3( 5457, 15373, 22298) },
                    { CDF3(31697, 32706, 32748) },
                    { CDF3(17860, 30657, 32333) },
                    { CDF3(12510, 24812, 29261) },
                    { CDF3( 6180, 19124, 24722) },
                    { CDF3( 5041, 13548, 17959) },
                    { CDF3(31552, 32716, 32748) },
                    { CDF3(21908, 31769, 32623) },
                    { CDF3(14470, 28201, 31565) },
                    { CDF3( 9493, 22982, 28608) },
                    { CDF3( 6858, 17240, 24137) },
                    { CDF3(32543, 32752, 32756) },
                    { CDF3(24286, 32097, 32666) },
                    { CDF3(15958, 29217, 32024) },
                    { CDF3(10207, 24234, 29958) },
                    { CDF3( 6929, 18305, 25652) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            }, {
                {
                    { CDF3( 4137, 10847, 15682) },
                    { CDF3(17824, 27001, 30058) },
                    { CDF3(10204, 22796, 28291) },
                    { CDF3( 6076, 15935, 22125) },
                    { CDF3( 3852, 10937, 16816) },
                    { CDF3( 2252,  6324, 10131) },
                    { CDF3(25840, 32016, 32662) },
                    { CDF3(15109, 28268, 31531) },
                    { CDF3( 9385, 22231, 28340) },
                    { CDF3( 6082, 16672, 23479) },
                    { CDF3( 3318,  9427, 14681) },
                    { CDF3(30594, 32574, 32718) },
                    { CDF3(16836, 29552, 31859) },
                    { CDF3( 9556, 22542, 28356) },
                    { CDF3( 6305, 16725, 23540) },
                    { CDF3( 3376,  9895, 15184) },
                    { CDF3(29383, 32617, 32745) },
                    { CDF3(18891, 30809, 32401) },
                    { CDF3(11688, 25942, 30687) },
                    { CDF3( 7468, 19469, 26651) },
                    { CDF3( 3909, 11358, 17012) },
                    { CDF3(31564, 32736, 32748) },
                    { CDF3(20906, 31611, 32600) },
                    { CDF3(13191, 27621, 31537) },
                    { CDF3( 8768, 22029, 28676) },
                    { CDF3( 5079, 14109, 20906) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                }, {
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                    { CDF3( 8192, 16384, 24576) },
                },
            },
        }, .dc_sign = {
            { { CDF1(16000) }, { CDF1(13056) }, { CDF1(18816) } },
            { { CDF1(15232) }, { CDF1(12928) }, { CDF1(17280) } },
        }, .br_tok = {
            {
                {
                    { CDF3(18315, 24289, 27551) },
                    { CDF3(16854, 24068, 27835) },
                    { CDF3(10140, 17927, 23173) },
                    { CDF3( 6722, 12982, 18267) },
                    { CDF3( 4661,  9826, 14706) },
                    { CDF3( 3832,  8165, 12294) },
                    { CDF3( 2795,  6098,  9245) },
                    { CDF3(17145, 23326, 26672) },
                    { CDF3(20733, 27680, 30308) },
                    { CDF3(16032, 24461, 28546) },
                    { CDF3(11653, 20093, 25081) },
                    { CDF3( 9290, 16429, 22086) },
                    { CDF3( 7796, 14598, 19982) },
                    { CDF3( 6502, 12378, 17441) },
                    { CDF3(21681, 27732, 30320) },
                    { CDF3(22389, 29044, 31261) },
                    { CDF3(19027, 26731, 30087) },
                    { CDF3(14739, 23755, 28624) },
                    { CDF3(11358, 20778, 25511) },
                    { CDF3(10995, 18073, 24190) },
                    { CDF3( 9162, 14990, 20617) },
                }, {
                    { CDF3(21425, 27952, 30388) },
                    { CDF3(18062, 25838, 29034) },
                    { CDF3(11956, 19881, 24808) },
                    { CDF3( 7718, 15000, 20980) },
                    { CDF3( 5702, 11254, 16143) },
                    { CDF3( 4898,  9088, 16864) },
                    { CDF3( 3679,  6776, 11907) },
                    { CDF3(23294, 30160, 31663) },
                    { CDF3(24397, 29896, 31836) },
                    { CDF3(19245, 27128, 30593) },
                    { CDF3(13202, 19825, 26404) },
                    { CDF3(11578, 19297, 23957) },
                    { CDF3( 8073, 13297, 21370) },
                    { CDF3( 5461, 10923, 19745) },
                    { CDF3(27367, 30521, 31934) },
                    { CDF3(24904, 30671, 31940) },
                    { CDF3(23075, 28460, 31299) },
                    { CDF3(14400, 23658, 30417) },
                    { CDF3(13885, 23882, 28325) },
                    { CDF3(14746, 22938, 27853) },
                    { CDF3( 5461, 16384, 27307) },
                },
            }, {
                {
                    { CDF3(18274, 24813, 27890) },
                    { CDF3(15537, 23149, 27003) },
                    { CDF3( 9449, 16740, 21827) },
                    { CDF3( 6700, 12498, 17261) },
                    { CDF3( 4988,  9866, 14198) },
                    { CDF3( 4236,  8147, 11902) },
                    { CDF3( 2867,  5860,  8654) },
                    { CDF3(17124, 23171, 26101) },
                    { CDF3(20396, 27477, 30148) },
                    { CDF3(16573, 24629, 28492) },
                    { CDF3(12749, 20846, 25674) },
                    { CDF3(10233, 17878, 22818) },
                    { CDF3( 8525, 15332, 20363) },
                    { CDF3( 6283, 11632, 16255) },
                    { CDF3(20466, 26511, 29286) },
                    { CDF3(23059, 29174, 31191) },
                    { CDF3(19481, 27263, 30241) },
                    { CDF3(15458, 23631, 28137) },
                    { CDF3(12416, 20608, 25693) },
                    { CDF3(10261, 18011, 23261) },
                    { CDF3( 8016, 14655, 19666) },
                }, {
                    { CDF3(17616, 24586, 28112) },
                    { CDF3(15809, 23299, 27155) },
                    { CDF3(10767, 18890, 23793) },
                    { CDF3( 7727, 14255, 18865) },
                    { CDF3( 6129, 11926, 16882) },
                    { CDF3( 4482,  9704, 14861) },
                    { CDF3( 3277,  7452, 11522) },
                    { CDF3(22956, 28551, 30730) },
                    { CDF3(22724, 28937, 30961) },
                    { CDF3(18467, 26324, 29580) },
                    { CDF3(13234, 20713, 25649) },
                    { CDF3(11181, 17592, 22481) },
                    { CDF3( 8291, 18358, 24576) },
                    { CDF3( 7568, 11881, 14984) },
                    { CDF3(24948, 29001, 31147) },
                    { CDF3(25674, 30619, 32151) },
                    { CDF3(20841, 26793, 29603) },
                    { CDF3(14669, 24356, 28666) },
                    { CDF3(11334, 23593, 28219) },
                    { CDF3( 8922, 14762, 22873) },
                    { CDF3( 8301, 13544, 20535) },
                },
            }, {
                {
                    { CDF3(17113, 23733, 27081) },
                    { CDF3(14139, 21406, 25452) },
                    { CDF3( 8552, 15002, 19776) },
                    { CDF3( 5871, 11120, 15378) },
                    { CDF3( 4455,  8616, 12253) },
                    { CDF3( 3469,  6910, 10386) },
                    { CDF3( 2255,  4553,  6782) },
                    { CDF3(18224, 24376, 27053) },
                    { CDF3(19290, 26710, 29614) },
                    { CDF3(14936, 22991, 27184) },
                    { CDF3(11238, 18951, 23762) },
                    { CDF3( 8786, 15617, 20588) },
                    { CDF3( 7317, 13228, 18003) },
                    { CDF3( 5101,  9512, 13493) },
                    { CDF3(22639, 28222, 30210) },
                    { CDF3(23216, 29331, 31307) },
                    { CDF3(19075, 26762, 29895) },
                    { CDF3(15014, 23113, 27457) },
                    { CDF3(11938, 19857, 24752) },
                    { CDF3( 9942, 17280, 22282) },
                    { CDF3( 7167, 13144, 17752) },
                }, {
                    { CDF3(15820, 22738, 26488) },
                    { CDF3(13530, 20885, 25216) },
                    { CDF3( 8395, 15530, 20452) },
                    { CDF3( 6574, 12321, 16380) },
                    { CDF3( 5353, 10419, 14568) },
                    { CDF3( 4613,  8446, 12381) },
                    { CDF3( 3440,  7158,  9903) },
                    { CDF3(24247, 29051, 31224) },
                    { CDF3(22118, 28058, 30369) },
                    { CDF3(16498, 24768, 28389) },
                    { CDF3(12920, 21175, 26137) },
                    { CDF3(10730, 18619, 25352) },
                    { CDF3(10187, 16279, 22791) },
                    { CDF3( 9310, 14631, 22127) },
                    { CDF3(24970, 30558, 32057) },
                    { CDF3(24801, 29942, 31698) },
                    { CDF3(22432, 28453, 30855) },
                    { CDF3(19054, 25680, 29580) },
                    { CDF3(14392, 23036, 28109) },
                    { CDF3(12495, 20947, 26650) },
                    { CDF3(12442, 20326, 26214) },
                },
            }, {
                {
                    { CDF3(12162, 18785, 22648) },
                    { CDF3(12749, 19697, 23806) },
                    { CDF3( 8580, 15297, 20346) },
                    { CDF3( 6169, 11749, 16543) },
                    { CDF3( 4836,  9391, 13448) },
                    { CDF3( 3821,  7711, 11613) },
                    { CDF3( 2228,  4601,  7070) },
                    { CDF3(16319, 24725, 28280) },
                    { CDF3(15698, 23277, 27168) },
                    { CDF3(12726, 20368, 25047) },
                    { CDF3( 9912, 17015, 21976) },
                    { CDF3( 7888, 14220, 19179) },
                    { CDF3( 6777, 12284, 17018) },
                    { CDF3( 4492,  8590, 12252) },
                    { CDF3(23249, 28904, 30947) },
                    { CDF3(21050, 27908, 30512) },
                    { CDF3(17440, 25340, 28949) },
                    { CDF3(14059, 22018, 26541) },
                    { CDF3(11288, 18903, 23898) },
                    { CDF3( 9411, 16342, 21428) },
                    { CDF3( 6278, 11588, 15944) },
                }, {
                    { CDF3(13981, 20067, 23226) },
                    { CDF3(16922, 23580, 26783) },
                    { CDF3(11005, 19039, 24487) },
                    { CDF3( 7389, 14218, 19798) },
                    { CDF3( 5598, 11505, 17206) },
                    { CDF3( 6090, 11213, 15659) },
                    { CDF3( 3820,  7371, 10119) },
                    { CDF3(21082, 26925, 29675) },
                    { CDF3(21262, 28627, 31128) },
                    { CDF3(18392, 26454, 30437) },
                    { CDF3(14870, 22910, 27096) },
                    { CDF3(12620, 19484, 24908) },
                    { CDF3( 9290, 16553, 22802) },
                    { CDF3( 6668, 14288, 20004) },
                    { CDF3(27704, 31055, 31949) },
                    { CDF3(24709, 29978, 31788) },
                    { CDF3(21668, 29264, 31657) },
                    { CDF3(18295, 26968, 30074) },
                    { CDF3(16399, 24422, 29313) },
                    { CDF3(14347, 23026, 28104) },
                    { CDF3(12370, 19806, 24477) },
                },
            },
        },
    }
};

void dav1d_cdf_thread_update(const Dav1dFrameHeader *const hdr,
                             CdfContext *const dst,
                             const CdfContext *const src)
{
#define update_cdf_1d(n1d, name) \
    do { \
        memcpy(dst->name, src->name, sizeof(dst->name)); \
        dst->name[n1d] = 0; \
    } while (0)

#define update_cdf_2d(n1d, n2d, name) \
    for (int j = 0; j < (n1d); j++) update_cdf_1d(n2d, name[j])
#define update_cdf_3d(n1d, n2d, n3d, name) \
    for (int k = 0; k < (n1d); k++) update_cdf_2d(n2d, n3d, name[k])
#define update_cdf_4d(n1d, n2d, n3d, n4d, name) \
    for (int l = 0; l < (n1d); l++) update_cdf_3d(n2d, n3d, n4d, name[l])

#define update_bit_0d(name) \
    do { \
        dst->name[0] = src->name[0]; \
        dst->name[1] = 0; \
    } while (0)

#define update_bit_1d(n1d, name) \
    for (int i = 0; i < (n1d); i++) update_bit_0d(name[i])
#define update_bit_2d(n1d, n2d, name) \
    for (int j = 0; j < (n1d); j++) update_bit_1d(n2d, name[j])
#define update_bit_3d(n1d, n2d, n3d, name) \
    for (int k = 0; k < (n1d); k++) update_bit_2d(n2d, n3d, name[k])

    update_bit_1d(N_BS_SIZES, m.use_filter_intra);
    update_cdf_1d(4, m.filter_intra);
    update_cdf_3d(2, N_INTRA_PRED_MODES, N_UV_INTRA_PRED_MODES - 1 - !k, m.uv_mode);
    update_cdf_2d(8, 6, m.angle_delta);
    update_cdf_3d(N_TX_SIZES - 1, 3, imin(k + 1, 2), m.txsz);
    update_cdf_3d(2, N_INTRA_PRED_MODES, 6, m.txtp_intra1);
    update_cdf_3d(3, N_INTRA_PRED_MODES, 4, m.txtp_intra2);
    update_bit_1d(3, m.skip);
    update_cdf_3d(N_BL_LEVELS, 4, dav1d_partition_type_count[k], m.partition);
    update_bit_2d(N_TX_SIZES, 13, coef.skip);
    update_cdf_3d(2, 2, 4, coef.eob_bin_16);
    update_cdf_3d(2, 2, 5, coef.eob_bin_32);
    update_cdf_3d(2, 2, 6, coef.eob_bin_64);
    update_cdf_3d(2, 2, 7, coef.eob_bin_128);
    update_cdf_3d(2, 2, 8, coef.eob_bin_256);
    update_cdf_2d(2, 9, coef.eob_bin_512);
    update_cdf_2d(2, 10, coef.eob_bin_1024);
    update_bit_3d(N_TX_SIZES, 2, 11 /*22*/, coef.eob_hi_bit);
    update_cdf_4d(N_TX_SIZES, 2, 4, 2, coef.eob_base_tok);
    update_cdf_4d(N_TX_SIZES, 2, 41 /*42*/, 3, coef.base_tok);
    update_bit_2d(2, 3, coef.dc_sign);
    update_cdf_4d(4, 2, 21, 3, coef.br_tok);
    update_cdf_2d(3, DAV1D_MAX_SEGMENTS - 1, m.seg_id);
    update_cdf_1d(7, m.cfl_sign);
    update_cdf_2d(6, 15, m.cfl_alpha);
    update_bit_0d(m.restore_wiener);
    update_bit_0d(m.restore_sgrproj);
    update_cdf_1d(2, m.restore_switchable);
    update_cdf_1d(3, m.delta_q);
    update_cdf_2d(5, 3, m.delta_lf);
    update_bit_2d(7, 3, m.pal_y);
    update_bit_1d(2, m.pal_uv);
    update_cdf_3d(2, 7, 6, m.pal_sz);
    update_cdf_4d(2, 7, 5, k + 1, m.color_map);
    update_bit_2d(7, 3, m.txpart);
    update_cdf_2d(2, 15, m.txtp_inter1);
    update_cdf_1d(11, m.txtp_inter2);
    update_bit_1d(4, m.txtp_inter3);

    if (IS_KEY_OR_INTRA(hdr)) {
        update_bit_0d(m.intrabc);

        update_cdf_1d(N_MV_JOINTS - 1, dmv.joint);
        for (int k = 0; k < 2; k++) {
            update_cdf_1d(10, dmv.comp[k].classes);
            update_bit_0d(dmv.comp[k].class0);
            update_bit_1d(10, dmv.comp[k].classN);
            update_bit_0d(dmv.comp[k].sign);
        }
        return;
    }

    update_bit_1d(3, m.skip_mode);
    update_cdf_2d(4, N_INTRA_PRED_MODES - 1, m.y_mode);
    update_cdf_3d(2, 8, DAV1D_N_SWITCHABLE_FILTERS - 1, m.filter);
    update_bit_1d(6, m.newmv_mode);
    update_bit_1d(2, m.globalmv_mode);
    update_bit_1d(6, m.refmv_mode);
    update_bit_1d(3, m.drl_bit);
    update_cdf_2d(8, N_COMP_INTER_PRED_MODES - 1, m.comp_inter_mode);
    update_bit_1d(4, m.intra);
    update_bit_1d(5, m.comp);
    update_bit_1d(5, m.comp_dir);
    update_bit_1d(6, m.jnt_comp);
    update_bit_1d(6, m.mask_comp);
    update_bit_1d(9, m.wedge_comp);
    update_cdf_2d(9, 15, m.wedge_idx);
    update_bit_2d(6, 3, m.ref);
    update_bit_2d(3, 3, m.comp_fwd_ref);
    update_bit_2d(2, 3, m.comp_bwd_ref);
    update_bit_2d(3, 3, m.comp_uni_ref);
    update_bit_1d(3, m.seg_pred);
    update_bit_1d(4, m.interintra);
    update_bit_1d(7, m.interintra_wedge);
    update_cdf_2d(4, 3, m.interintra_mode);
    update_cdf_2d(N_BS_SIZES, 2, m.motion_mode);
    update_bit_1d(N_BS_SIZES, m.obmc);

    update_cdf_1d(N_MV_JOINTS - 1, mv.joint);
    for (int k = 0; k < 2; k++) {
        update_cdf_1d(10, mv.comp[k].classes);
        update_bit_0d(mv.comp[k].class0);
        update_bit_1d(10, mv.comp[k].classN);
        update_cdf_2d(2, 3, mv.comp[k].class0_fp);
        update_cdf_1d(3, mv.comp[k].classN_fp);
        update_bit_0d(mv.comp[k].class0_hp);
        update_bit_0d(mv.comp[k].classN_hp);
        update_bit_0d(mv.comp[k].sign);
    }
}

/*
 * CDF threading wrappers.
 */
static inline int get_qcat_idx(const int q) {
    if (q <= 20) return 0;
    if (q <= 60) return 1;
    if (q <= 120) return 2;
    return 3;
}

void dav1d_cdf_thread_init_static(CdfThreadContext *const cdf, const int qidx) {
    cdf->ref = NULL;
    cdf->data.qcat = get_qcat_idx(qidx);
}

void dav1d_cdf_thread_copy(CdfContext *const dst, const CdfThreadContext *const src) {
    if (src->ref) {
        memcpy(dst, src->data.cdf, sizeof(*dst));
    } else {
        dst->m = av1_default_cdf;
        memcpy(dst->kfym, default_kf_y_mode_cdf, sizeof(default_kf_y_mode_cdf));
        dst->coef = av1_default_coef_cdf[src->data.qcat];
        memcpy(dst->mv.joint, default_mv_joint_cdf, sizeof(default_mv_joint_cdf));
        memcpy(dst->dmv.joint, default_mv_joint_cdf, sizeof(default_mv_joint_cdf));
        dst->mv.comp[0] = dst->mv.comp[1] = dst->dmv.comp[0] = dst->dmv.comp[1] =
            default_mv_component_cdf;
    }
}

int dav1d_cdf_thread_alloc(Dav1dContext *const c, CdfThreadContext *const cdf,
                           const int have_frame_mt)
{
    cdf->ref = dav1d_ref_create_using_pool(c->cdf_pool,
                                           sizeof(CdfContext) + sizeof(atomic_uint));
    if (!cdf->ref) return DAV1D_ERR(ENOMEM);
    cdf->data.cdf = cdf->ref->data;
    if (have_frame_mt) {
        cdf->progress = (atomic_uint *) &cdf->data.cdf[1];
        atomic_init(cdf->progress, 0);
    }
    return 0;
}

void dav1d_cdf_thread_ref(CdfThreadContext *const dst,
                          CdfThreadContext *const src)
{
    *dst = *src;
    if (src->ref)
        dav1d_ref_inc(src->ref);
}

void dav1d_cdf_thread_unref(CdfThreadContext *const cdf) {
    memset(&cdf->data, 0, sizeof(*cdf) - offsetof(CdfThreadContext, data));
    dav1d_ref_dec(&cdf->ref);
}
