# The dav1d project and VideoLAN association would like to thank

## AOM
The Alliance for Open Media (AOM) for partially funding this project.

## Companies
* Two Orioles LLC, for important coding effort
* VideoLabs SAS

## Projects
* VideoLAN
* FFmpeg
* libplacebo

## Individual

And all the dav1d Authors (git shortlog -sn), including:

<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,
<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,
<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,
<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,
<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,
<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,
<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,
<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,
<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
<PERSON><PERSON>, <PERSON> <PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,
<PERSON> <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>
